package com.btkj.fuyou.wrules;

import com.aliyun.OSSUtil;
import com.aliyun.oss.properties.OssConfig;
import com.aliyun.oss.properties.OssProperties;
import com.btkj.fuyou.core.constant.SecurityConstants;
import com.btkj.fuyou.core.security.utils.SecurityUtils;
import com.btkj.fuyou.core.util.SpringUtils;
import com.btkj.fuyou.service.ISaveMessageService;
import com.btkj.fuyou.service.feign.SendMessageService;
import com.btkj.fuyou.util.SpringContextUtil;
import com.btkj.fuyou.util.ZIPUtil;
import com.btkj.fuyou.wrules.check.CheckPublic;
import com.btkj.fuyou.wrules.check.CheckSaveHandler;
import com.itextpdf.kernel.geom.PageSize;
import com.whoami.core.Global;
import com.whoami.db.DB;
import com.whoami.db.DBUtil;
import com.whoami.util.FileUtil;
import com.whoami.util.HttpUtil;
import com.whoami.util.PinYinUtil;
import com.whoami.util.StringUtil;
import org.apache.commons.io.FileUtils;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONArray;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.whoami.util.FileUtil.close;

public class FuYouHandler extends Thread {
    public static Logger log = org.apache.logging.log4j.LogManager.getLogger(FuYouHandler.class);

    private String[] p;
    private String[] cp;
    private HttpServletRequest request;

    public FuYouHandler(String[] p, String[] cp, HttpServletRequest request) {
        super();
        this.p = p;
        this.cp = cp;
        this.request = request;
        start();
    }

    //线程运行
    public void run() {
        if (p.length > 3) {
            if ("reportrecipe".equals(p[3])) {
                String pno = p[4];
                String cno = p[5];
                int type = Integer.valueOf(p[7]);
                String sql = "";
                if (type == 1) {//发布
                    int ispublic = Integer.valueOf(p[6]);
                    String swhere = "";
                    if (ispublic > 0) {
                        swhere += " and ispublic&" + ispublic + "!=" + ispublic;
                    } else {
                        swhere += " and ispublic&" + Math.abs(ispublic) + "=" + Math.abs(ispublic);
                    }
                    sql = "update ek_recipe set ispublic=ispublic+" + ispublic + " where  isdel=0 and pno='" + pno + "' and cno='" + cno + "'" + swhere;
                } else {//分享
                    int isshare = Integer.valueOf(p[6]);
                    String swhere = "";
                    if (isshare > 0) {
                        swhere += " and isshare&" + isshare + "!=" + isshare;
                    } else {
                        swhere += " and isshare&" + Math.abs(isshare) + "=" + Math.abs(isshare);
                    }
                    sql = "update ek_recipe set isshare=isshare+" + isshare + " where  isdel=0 and pno='" + pno + "' and cno='" + cno + "'" + swhere;
                }
                Connection con = null;
                Statement st = null;
                try { //批量执行语句
                    con = DB.getCon(cp[0], cp[1]);
                    st = con.createStatement();
                    con.setAutoCommit(false);//取消自动提交
                    st.addBatch(sql);
                    //执行批处理语句
                    st.executeBatch();
                    String postresult = fuyou_post(p, cp, request);
                    JSONObject objpostresult = new JSONObject(postresult);
                    String return_code = objpostresult.optString("return_code");
                    if (return_code.equals("1")) {
                        con.commit();
                        log.info("食谱上报云妇幼执行成功");
                    } else {
                        con.rollback();
                    }
                    con.close();
                } catch (SQLException e) {
                    log.error("e.getMessage()", e);
                    //将数据回滚
                    try {
                        con.rollback();
                    } catch (SQLException e1) {
                        log.error(e1.getMessage());
                    }
                    log.info("食谱上报云妇幼执行失败，执行语句出错" + sql);
                } finally {
                    try {
                        DB.close(con, st);
                    } catch (Exception e) {
                        log.error("FuYouHandler_reportrecipe", e);
                    }
                }
            }
        } else {
            fuyou_post(p, cp, request);
        }
    }

    //发送post请求
    public static String fuyou_post(String[] p, String[] cp, HttpServletRequest request) {
        log.info(p[1] + ":" + p[2]);
        String res = "";
        try {
            JSONObject data = new JSONObject(p[2]);
            if (!p[3].equals("#2#")) {
                data.put("where", p[3]);
            }
            data.put("yeyid", SecurityUtils.getAttribute("yeyid"));
            data.put("from", "local");
            JSONObject objresult;
            switch (p[1]) {
                case "reportlist.data":
                    res = ReportHandler.getReportList(data, request);
                    objresult = new JSONObject(res);
                    res = "{\"re\":" + (objresult.has("data") ? String.valueOf(objresult.get("data")) : "\"执行成功！\"") + "}";
                    break;
                case "report.count":
                    res = ReportHandler.getReportCount(data, request);
                    objresult = new JSONObject(res);
                    res = "{\"re\":" + (objresult.has("data") ? String.valueOf(objresult.get("data")) : "\"执行成功！\"") + "}";
                    break;
                case "report.sb":
                    String yeyname = "";
                    String cguid = PostHandler.returnGuid(data.getString("areacode"), request);
                    JSONObject obj = DBUtil.selectJSON("f", cguid, "select yeyname from yey where id=" + SecurityUtils.getAttribute("yeyid"));
                    if (obj.getInt("length") > 0) {
                        yeyname = obj.getJSONArray("arrdata").getJSONObject(0).optString("yeyname");
                    }
                    data.put("yeyname", yeyname);
                    data.put("dljgmc", yeyname);
                    res = ReportHandler.sbReport(data, request);
                    break;
                case "report.getlog":
                    res = ReportHandler.getLog(data, request);
                    objresult = new JSONObject(res);
                    res = "{\"re\":" + (objresult.has("data") ? String.valueOf(objresult.get("data")) : "\"执行成功！\"") + "}";
                    break;
                default:
                    break;
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("fuyou_post", ex);
            res = "{\"error\":\"程序异常\"}";
        }
        return res;
    }

    /**
     * fuyou解除,同意申请或拒绝申请时更新幼儿园端的通知状态信息
     *
     * @param objson
     * @param request
     * @return
     */
    public static String UpJoinNotice(JSONObject objson, HttpServletRequest request) {
        try {
            String cztype = objson.optString("cztype"),
                    did = objson.optString("did"),
                    fuyouareacode = objson.optString("fuyouareacode"),
                    sqlnotice = "",//通知信息
                    where = "";
            switch (cztype) {
                case "1"://拒绝
                    //处理幼儿园端通知记录
                    sqlnotice = "agreestatus=1,agreetime=now()";
                    where = " and agreestatus is null";
                    break;
                case "2"://同意
                    where = " and agreestatus is null";
                    //2.处理幼儿园端通知记录
                    sqlnotice = "agreestatus=2,agreetime=now()";
                    break;
                case "3"://解除
                    //处理幼儿园通知记录
                    sqlnotice = "relieve=1,relievetime=now()";
                    where = " and relieve is null and agreestatus=2";
                    break;
            }
            JSONObject obj = DBUtil.update("y", did, "fuyou_joinnotice", sqlnotice, " isdel=0 and jtype=2 and areacode='" + fuyouareacode + "'" + where, request);
            if (!obj.has("re")) {
                return "";
            } else {
                log.info("更新园所通知信息成功");
                return "1";
            }
        } catch (Exception e) {
            log.error(e);
            return "";
        }
    }

    /**
     * 妇幼申请开通园所，童帮人审核通过后增加处理园所加入的通知记录
     *
     * @param objson
     * @param request
     * @return
     */
    public static String AddNoticeFromFuyou(JSONObject objson, HttpServletRequest request) {
        try {
            String cztype = objson.optString("cztype"),
                    guid = objson.optString("did"),
                    fuyouareacode = objson.optString("fuyouareacode"),
                    areaname = objson.optString("areaname"),
                    joinname = objson.optString("joinname"),
                    sendtime = objson.optString("sendtime"),
                    agreestatus = objson.optString("agreestatus"),
                    agreetime = objson.optString("agreetime"),
                    sqlnotice = "",//通知信息
                    where = "";
            //jtype 1邀请  2申请
            if ("2".equals(cztype)) {
                JSONObject obj = DBUtil.insert("y", guid, "fuyou_joinnotice", "areacode,areaname,jtype,joinname,sendtime,agreestatus,agreetime", "'" + fuyouareacode + "','" + areaname + "',1,'" + joinname + "',current_timestamp, " + agreestatus + ",current_timestamp", "isdel=0 and agreestatus is null and relieve is null and areacode='" + fuyouareacode + "'", request);
                if (!obj.has("re")) {
                    return "";
                } else {
                    log.info("更新园所通知信息成功");
                    return "1";
                }
            }
        } catch (Exception e) {
            log.error(e);
        }
        return "";
    }

    /**
     * @param objson
     * @param request
     * @return
     */
    public static String GetBabyInfo(JSONObject objson, HttpServletRequest request) {
        try {
            String did = objson.optString("did"),
                    today = objson.optString("today"),
                    updatetime = objson.optString("updatetime"),
                    areacode1 = objson.optString("areacode1"),
                    sqlnotice = "",//通知信息
                    where = "";

            where = "s.isdel=0 and (status=0 or (status!=0 and outtime>'" + today + "'))" + "";//条件
            if (StringUtil.notNull(updatetime)) {//更新时间处理下
                where = " altime>='" + updatetime + "'";
            }
            //读取当前时间幼儿信息
            String sql = "select ((case when isdel >0 then 1 when isdel=0 and status!=0 and outtime<='" + today + "' then 1 else 0 end)) as isdel,stuno,stuname,sex,to_char(birthday,'YYYY-MM-DD') as birthday,classno,faphone,mophone,otherphone,lng,lat,filenumber,nation, to_char(intime,'YYYY-MM-DD') as intime,intype,place,nationality,locationtype,localtion,szs,allergy,disease,mapplace,status,outtime,sp,spe from ek_student as s  where intime <= '" + today + "' and " + where;
            JSONObject obj = DBUtil.selectJSON("y", did, sql);
            JSONArray arrdata = obj.getJSONArray("arrdata");
            if (obj.getInt("length") > 0) {
                return arrdata.toString();
            } else {
                return "";
            }
        } catch (Exception e) {
            log.error(e);
            return "";
        }
    }

    //获取上报地址
    public static String getReportUrl(String[] p, String[] cp, HttpServletRequest request) {
        log.info(p[1]);
        String res = "";
        try {
//            String url = Config.map.get("fuyouAddress");
            String url = "";
            String areacode = p[1];
            if (StringUtil.notNull(areacode)) {
                HashMap<String, String> params2 = new HashMap<String, String>();
                params2.put("t", "tongbang");
                params2.put("type", "getreportrul");
                params2.put("data", URLEncoder.encode(areacode, "utf-8"));
                params2.put("timestamp", String.valueOf(new Date().getTime()));
                String str2 = HttpUtil.post(Config.map.get("fyyyptAddress"), params2, 10000, 10000, "UTF-8");
                if (StringUtil.notNull(str2)) {
//                    log.info(str2);
                    JSONObject json = new JSONObject(str2);
                    String return_code = json.optString("re");
                    if (return_code.equals("1")) {
                        if (json.has("data")) {
                            JSONArray arrreporturl = json.optJSONArray("data");
                            JSONObject objreporturl = arrreporturl.optJSONObject(0);
//                            objreporturl.put("reporturl","http://localhost:8087/fuyou/post");
                            return "{\"re\":" + objreporturl.toString() + "}";
//							if(objreporturl.has("reporturl") && StringUtil.notNull(objreporturl.optString("reporturl"))){
//								url = objreporturl.optString("reporturl");
//							}
                        }
                    }
                }
            }
//			if(url != null){
//                res = "{\"re\":\"" + url + "\"}";
//			}else{
            res = "{\"error\":\"返回为空\"}";
//			}
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
            res = "{\"error\":\"程序异常\"}";
        }
        return res;
    }

    //获取登录码
    public static String getLoginAuthCode(String[] p, String[] cp, HttpServletRequest request) {
        String res = "";
        try {
            log.info("getLoginAuthCode:" + p[1]);
            JSONObject objson = new JSONObject(p[1]);
//            String uname = (String) SecurityUtils.getAttribute("username");
            String yeyid = (String) SecurityUtils.getAttribute("yeyid");
//            objson.put("uname", uname);
            objson.put("dtype", "y");
            objson.put("yeyid", yeyid);
            String url = Config.map.get("fuyouAddress");
            if (objson.has("cguid")) {
                HashMap<String, String> params2 = new HashMap<String, String>();
                params2.put("t", "getLoginAuthCode");
                params2.put("type", "getLoginAuthCode");
                params2.put("data", URLEncoder.encode(objson.toString(), "utf-8"));
                params2.put("timestamp", String.valueOf(new Date().getTime()));
                String strback = HttpUtil.post(Config.map.get("fyyyptAddress"), params2, 10000, 10000, "UTF-8");
                if (StringUtil.isNull(strback)) {
                    return "{\"error\":\"请求失败\"}";
                } else {
                    log.info(strback);
                    JSONObject objres = new JSONObject(strback);
                    if (objres.has("code")) {
                        return "{\"error\":\"" + objres.optString("msg") + "\"}";
                    } else {
                        return strback;
                    }
                }
            }
            res = "{\"error\":\"返回为空\"}";
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
            res = "{\"error\":\"程序异常\"}";
        }
        return res;
    }

    /**
     * 获取体检可比
     *
     * @param objoneye 去年数据
     * @param reitem   当前数据
     * @param request
     * @return
     */
    public static String GetMdicalCompare(JSONObject objoneye, JSONObject reitem, int hight, int weight, String colv, String col, HttpServletRequest request) {
        try {
            int lasth = objoneye.optInt("hight");
            int lastw = objoneye.optInt("weight");
//            if("贾鑫洋".equals(reitem.optString("stuname"))){
//                log.info(reitem.optString("stuname"));
//            }
            if ("inhwcom".equals(col) || "hwcop".equals(col) || "inhewe".equals(col)) {//身高体重可比
                int inhight = hight - lasth;//身高增长
                int inweight = weight - lastw;//体重增长
                if (!"上".equals(colv) && !"上".equals(objoneye.optString("hewe")) && StringUtil.notNull(objoneye.optString("hight")) && StringUtil.notNull(objoneye.optString("weight")) && StringUtil.notNull(reitem.optString("hight")) && StringUtil.notNull(reitem.optString("weight"))) {
                    if ("inhwcom".equals(col) && inhight > 0 && inweight > 0)
                        colv = "1"; //两年同期身高和体重都不为空并且两年同期身高别体重都不为“上”且身高体重增长都大于0的为1.
                    if ("hwcop".equals(col) && inhight != 0 && inweight != 0)
                        colv = "1"; //两年同期身高和体重都不为空并且两年同期身高别体重都不为“上”且身高增长、体重增长都不为空为1
                    if ("inhewe".equals(col) && inhight >= 5 && inweight >= 1.6) {//身高体重增长。0不合格   1合格 -1未处理 默认-1。
                        colv = "1";
                    }
                } else if (("上".equals(colv) || "上".equals(objoneye.optString("hewe"))) && "inhewe".equals(col)) {
                    colv = "-1";
                } else if ("inhewe".equals(col)) {
                    colv = "0";
                }
            }
            if ("inheight".equals(col)) {//身高增长，体重增长，身高体重均增长
                int inhight = (hight > 0 && lastw > 0) ? hight - lasth : 0;//身高增长
                colv = (lastw <= 0 || (hight <= 0 && inhight == 0)) ? "null" : inhight + "";
            }
            if ("inweight".equals(col)) {
                int inweight = (weight > 0 && lastw > 0) ? weight - lastw : 0;//体重增长
                colv = (lastw <= 0 || (weight <= 0 && inweight == 0)) ? "null" : inweight + "";
            }
            if ("inhewe".equals(col)) {
                if (StringUtil.notNull(objoneye.optString("hight")) && StringUtil.notNull(objoneye.optString("weight"))) {
                    int inhight = hight - lasth;//身高增长
                    int inweight = weight - lastw;//体重增长
                    if (!"上".equals(colv) && !"上".equals(objoneye.optString("hewe")) && inhight >= 5 && inweight >= 1.6) {//身高增长5cm体重增长1.6kg的人数，身高体重增长合格
                        colv = "1";
                    } else if ("上".equals(colv) || "上".equals(objoneye.optString("hewe"))) {
                        colv = "-1";
                    } else {
                        colv = "0";
                    }
                    if (hight <= 0 && weight <= 0) {
                        colv = "-1";
                    }
                } else {
                    colv = "-1";
                }
            }

            if ("carcom1".equals(col) || "carcom2".equals(col)) {//龋齿可比
                int checked = reitem.optInt("checked");
                int lastchecked = objoneye.optInt("checked");
                if (checked == 1 && lastchecked == 1) {
                    if ("carcom1".equals(col)) {
                        colv = "1";
                    }
                    if ("carcom2".equals(col) && lasth > 0 && lastw > 0 && hight > 0 && weight > 0) {
                        colv = "1";
                    }
                }
            }
            if ("yyblnew".equals(col) || "yyblcom2".equals(col) || "overfatcom2".equals(col) || "fatcom2".equals(col)) {//yyblcom2身高体重统计中营养不良可比。0无  1 有。默认为0。,overfatcom2超重可比,fatcom2身高体重统计中肥胖可比。0无  1 有。默认为0。有两年同期对比肥胖检查结果的为1.
                //营养不良，超重，肥胖
                if (lasth > 0 && lastw > 0 && hight > 0 && weight > 0) {
                    colv = "1";
                    if ("yyblnew".equals(col) && StringUtil.notNull(reitem.optString("malnu")) && !"正常".equals(reitem.optString("malnu")) && (StringUtil.isNull(objoneye.optString("malnu")) || "正常".equals(objoneye.optString("malnu")))) {//营养不良新发  malnustrcolumn[37]
                        colv = "1";//yyblnew = 1;//营养不良是否新发。0否  1是
                    }
                    String wufat = objoneye.optString("wufat");
                    if ("overfatnew".equals(col) && "超重".equals(reitem.optString("wufat")) && !"超重".equals(wufat)) {//超重新发,wufat
                        colv = "1";//overfatnew = 1;
                    }
                    if ("fatnew".equals(col) && (StringUtil.notNull(reitem.optString("wufat")) && !"正常".equals(reitem.optString("wufat")) && !"超重".equals(reitem.optString("wufat"))) && (StringUtil.isNull(wufat) || "正常".equals(wufat) || "超重".equals(wufat))) {//肥胖新发
                        colv = "1";//fatnew = 1;
                    }
                }
            }
            if ("listencom1".equals(col) || "listencom2".equals(col)) {
                //既往听力可比 arrlast[15]ischeck
                if (reitem.optInt("ischeck") == 1 && objoneye.optInt("ischeck") == 1) {//listencom1 各项统计中听力可比。0无  1有。默认为0。有两年同期对比听力检查结果的为1.
                    if ("listencom1".equals(col)) {
                        colv = "1";//listencom1 = 1;//听力可比
                    }
                    if ("listencom2".equals(col) && lasth > 0 && lastw > 0 && hight > 0 && weight > 0) {
                        colv = "1";//listencom2 = 1;//身高体重统计中听力可比。0无  1 有。默认为0。有两年同期对比听力检查结果且两年同期的身高体重都大于0的为1.
                    }
                }
            }
            if ("anemiacom1".equals(col) || "anemiacom2".equals(col) || "anemianew".equals(col)) {
                if (reitem.optInt("hematin") > 0 && StringUtil.notNull(reitem.optString("hematindivision")) && StringUtil.notNull(objoneye.optString("hematindivision"))) {//strcolumn[44]血色素值，血红蛋白新发、可比处理//arrlast[17] hematindivision贫血诊断结果:正常 、轻度 、中度  、重度。strintcol//hematindivision strcolumn[28]
                    if ("anemiacom1".equals(col)) {
                        colv = "1";//anemiacom1 = 1;//各项统计中贫血可比。0无  1有。默认为0。有两年同期对比贫血检查结果的为1.
                    }
                    if ("anemiacom2".equals(col) && lasth > 0 && lastw > 0 && hight > 0 && weight > 0) {
                        colv = "1";//anemiacom2 = 1;//身高体重统计中贫血可比。0无  1 有。默认为0。有两年同期对比贫血检查结果且两年同期的身高体重都大于0的为1.
                    }
                    if ("anemianew".equals(col) && !"正常".equals(reitem.optString("hematindivision")) && "正常".equals(objoneye.optString("hematindivision"))) {//血红蛋白新发
                        colv = "1";//anemianew = 1;//贫血是否新发。0否  1是
                    }
                }
            }
        } catch (Exception e) {
            log.error(e);
            return colv;
        }
        return colv;
    }

    public static String checkfeedback(String guid, JSONObject objson, HttpServletRequest request) {
        String Result = "";
        try {
            String eventtitle = objson.optString("eventtitle");
            String eventcontent = objson.optString("eventcontent");
            String createid = objson.optString("createid");
            String createname = objson.optString("createname");
            String sql = "insert into fuyou_notice (eventtype,eventtitle,eventcontent,createid,createname) values ('checkfeedback','" + eventtitle + "','" + eventcontent + "'," + createid + ",'" + createname + "')";
            JSONObject objIn = DBUtil.insertSql("y", guid, sql);
            if (objIn.has("re")) {
                Result = "{\"re\":\"success\"}";
            } else {
                Result = "{\"error\":\"操作失败\"}";
            }
        } catch (Exception e) {
            log.error(e);
            Result = "{\"error\":\"操作失败\"}";
        }
        return Result;
    }

    /**
     * 下载复查照片
     *
     * @param strids
     * @param request
     * @return
     */
    public static String downloadImgs(String strids, HttpServletRequest request) {
        String cguid = (String) SecurityUtils.getAttribute("guid");
        int uid = (Integer) SecurityUtils.getAttribute("uid");
        String sql = "select check_id,yeyid,yeyname,stuno,stuname,feye_photos,car_photos,flis_photos,fxf_photos from check_child_result where id in (" + strids + ")";
        JSONArray array = DBUtil.selectArray("f", cguid, sql);
        if(array.length() == 0){
            return new JSONObject().put("error", "数据为空").toString();
        }
        //1、创建一级文件夹
        String uploadPath = request.getServletContext().getRealPath("upload");
        if (StringUtil.isNull(uploadPath)) {
            uploadPath = request.getServletContext().getRealPath(File.separator) + "upload";
            log.info("projpath:" + uploadPath);
        }
        int checkId = array.optJSONObject(0).optInt("check_id");
        String basePath = "recheck_" + checkId + "_" + uid;
        String topDirPath = uploadPath + "/" + basePath;
        try {
            File dir = new File(topDirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            String baseUri = "";
            StringBuffer hosturl = request.getRequestURL();
            hosturl.delete(hosturl.length() - request.getRequestURI().length(), hosturl.length()).toString();
            String url = request.getRequestURL().toString();
            //            if ("1".equals(Config.map.get("devmodel"))) {
            if (url.contains("http://localhost")) {
                baseUri = hosturl.toString() + "/fuyou/";
            } else {
                baseUri = hosturl.toString() + "/";
            }
            boolean islocal = false;
            String prefix = baseUri;//"https://" + Config.map.get("OSS_BUCKETNAME") + "." + Config.map.get("OSS_URI");
            if(!islocal){
                OssProperties ossProp = SpringUtils.getBean(OssProperties.class);
                prefix = "https://" + ossProp.getOSS_BUCKETNAME() + "." + ossProp.getOSS_URI();
            }
            for (int i = 0; i < array.length(); i++) {
                JSONObject obj = array.optJSONObject(i);
                String stuno = obj.optString("stuno");
                String stuname = obj.optString("stuname");
                String yeyname = obj.optString("yeyname");
                String feye_photos = obj.optString("feye_photos");
                String car_photos = obj.optString("car_photos");
                String flis_photos = obj.optString("flis_photos");
                String fxf_photos = obj.optString("fxf_photos");
                //2、创建已学生姓名命名的文件夹
                String stuDirPath = topDirPath + "/" + yeyname + "/" + stuname + "_" + stuno;
                File dirStu = new File(stuDirPath);
                if (!dirStu.exists()) {
                    dirStu.mkdirs();
                }
                log.info(stuname + "的feye_photos：" + feye_photos);
                log.info(stuname + "的car_photos：" + car_photos);
                if (StringUtil.isNull(feye_photos) && StringUtil.isNull(car_photos)
                        && StringUtil.isNull(flis_photos) && StringUtil.isNull(fxf_photos)) {//如果既没有视力复查照片也没有龋齿复查照片
                    continue;
                }
                if (StringUtil.notNull(feye_photos)) {
                    String[] arrEyeimg = feye_photos.split(",");
                    for (int j = 0; j < arrEyeimg.length; j++) {
                        try {
                            if (StringUtil.isNull(arrEyeimg[j])) {
                                continue;
                            }
                            File destFile = new File(stuDirPath + arrEyeimg[j].substring(arrEyeimg[j].lastIndexOf("/")));
                            FileUtils.copyURLToFile(new URL(prefix + arrEyeimg[j]), destFile);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
                if (StringUtil.notNull(car_photos)) {
                    String[] arrCarimg = car_photos.split(",");
                    for (int j = 0; j < arrCarimg.length; j++) {
                        try {
                            if (StringUtil.isNull(arrCarimg[j])) {
                                continue;
                            }
                            File destFile = new File(stuDirPath + arrCarimg[j].substring(arrCarimg[j].lastIndexOf("/")));
                            FileUtils.copyURLToFile(new URL(prefix + arrCarimg[j]), destFile);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
                if (StringUtil.notNull(flis_photos)) {
                    String[] arrLisimg = flis_photos.split(",");
                    for (int j = 0; j < arrLisimg.length; j++) {
                        try {
                            if (StringUtil.isNull(arrLisimg[j])) {
                                continue;
                            }
                            File destFile = new File(stuDirPath + arrLisimg[j].substring(arrLisimg[j].lastIndexOf("/")));
                            FileUtils.copyURLToFile(new URL(prefix + arrLisimg[j]), destFile);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
                if (StringUtil.notNull(fxf_photos)) {
                    String[] arrFxfimg = fxf_photos.split(",");
                    for (int j = 0; j < arrFxfimg.length; j++) {
                        try {
                            if (StringUtil.isNull(arrFxfimg[j])) {
                                continue;
                            }
                            File destFile = new File(stuDirPath + arrFxfimg[j].substring(arrFxfimg[j].lastIndexOf("/")));
                            FileUtils.copyURLToFile(new URL(prefix + arrFxfimg[j]), destFile);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
            //3、将文件夹压缩，并返回压缩文件路径
            ZIPUtil.compressManyToOne(topDirPath, topDirPath + ".zip");
            //4、删除文件夹
            //        deleteDir(dir);
            //5、启动线程，延时删除压缩文件
            //        delayDeleteFile(topDirPath+".zip", 20);
        } catch (Exception e){
            log.error("下载图片失败", e);
            return new JSONObject().put("error", "下载图片失败").toString();
        }
        return new JSONObject().put("path", "/upload/" + basePath + ".zip").toString();
    }

    /**
     * 发放准考证
     *
     * @param params
     * @param request
     * @return
     */
    public static String generateTicket(JSONObject params, HttpServletRequest request) {
        log.info("generateTicket：发放准考证。");
        //返回的信息
        JSONArray result = new JSONArray();
        //1、查询要发放证件的人员信息
        String strIds = params.optString("ids");
        String enrollSql = "select id,noticeid,areacode,areaname,yeyid,yeyname,enrollemployeeid,enrollname,enrollsex,to_char(enrollbirthday,'YYYY-MM-DD') as enrollbirthday,enrollidcard,enrolleducation,enrollfromgraduate,enrollsubject,enrollmobile,enrollphotopath,enrolleducationphoto,enrolljobphoto,enrollmark,enrollstatus,fromid,frommobile,fromname,fromopenid,fromtype,certid,ticketid,forwardenrollid from tb_noticeenroll where id in (" + strIds + ") and certid is not null and certid!=0";
        JSONArray arrEnroll = DBUtil.selectArray("fyyypt", "", enrollSql);
        JSONObject objTicketJSON = new JSONObject();
        List<String> arrSql = new ArrayList<>();
        for (int i = 0; i < arrEnroll.length(); i++) {
            JSONObject objEnroll = arrEnroll.optJSONObject(i);
            int enrollId = objEnroll.optInt("id");
            int forwardenrollId = objEnroll.optInt("forwardenrollid");//转发通知报名表的id
            String enrollName = objEnroll.optString("enrollname");
            int ticketId = objEnroll.optInt("ticketid");
            if (ticketId == 0) {
                result.put(enrollName + "没有对应的准考证待发");
                log.info(enrollName + "没有准考证信息");
                continue;
            }
            JSONObject objTicket = null;
            if (objTicketJSON.has("" + ticketId)) {
                objTicket = objTicketJSON.optJSONObject("" + ticketId);
            } else {
                String ticketSql = "select id,ticketname,examaddr,to_char(examdate,'yyyy-mm-dd') as examdate,examtime,tip from tb_noticeticket where id = " + ticketId;
                objTicket = DBUtil.selectOneJSON("fyyypt", "", ticketSql);
                objTicketJSON.put("" + ticketId, objTicket);
            }

            try {
                String ossPath = ticketToPdf(objEnroll, objTicket, request);
                JSONObject upRe = DBUtil.update("fyyypt", "", "tb_noticeenroll", "ticketstate = 2,ticketpath = '" + ossPath + "'", "id = " + enrollId);
                if (forwardenrollId > 0) {
                    DBUtil.update("fyyypt", "", "tb_noticeenroll", "ticketstate = 2,ticketpath = '" + ossPath + "'", "id = " + forwardenrollId);
                }
//                arrSql.add("update tb_noticeenroll set ticketstate = 2,ticketpath = " + ossPath + " where id = " + enrollId);
//                arrSql.add("update tb_noticeenroll set ticketstate = 2,ticketpath = " + ossPath + " where id = " + forwardenrollId);
                if (upRe.has("error")) {
                    result.put(enrollName + upRe.optString("error"));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                result.put(enrollName + e.getMessage());
            }
        }
//        DBUtil.ExecuteSqls("fyyypt", "", arrSql);
        if (result.length() > 0) {
            return new JSONObject().put("error", result).toString();
        }
        return new JSONObject().put("re", "发证成功").toString();
    }

    /**
     * 生成准考证pdf
     *
     * @param objEnroll
     * @param request
     * @return
     * @throws Exception
     */
    private static String ticketToPdf(JSONObject objEnroll, JSONObject objTicket, HttpServletRequest request) throws Exception {
        log.info("ticketToPdf");
        String enrollName = objEnroll.optString("enrollname");
        String ticketPath = "";
        InputStream inputStream = null;
        OutputStream outputStream = null;
        InputStream pdfIn = null;
        try {
            String sourcePath = FuYouHandler.class.getClassLoader().getResource("templates").getPath();
            String uploadPath = Global.ProjectPath + "userfiles";
            String midDir = "chengdu/fuyou/ticket";
            long startTime = System.currentTimeMillis();
            //html文件所在路径
            String templatePath = sourcePath + "/ticket/viewnoticeticket.html";
            File file = new File(uploadPath + "/" + midDir);
            if (!file.exists()) {
                file.mkdirs();
            }
            //pdf文件存储路径
            String idcard = objEnroll.optString("enrollidcard");
            String ticketfilename = "ticket_" + idcard + "_" + new Date().getTime() + Math.round(Math.random() * 1000) + ".pdf";
            String localPdfPath = uploadPath + "/" + midDir + "/" + ticketfilename;
            inputStream = new FileInputStream(templatePath);
            outputStream = new FileOutputStream(localPdfPath);
            //字体文件路劲
            String fontPath = sourcePath + "/healthcard/font/simsun.ttc,0";
            //图片，css等文件的基础路径
            String baseUri = "";
            StringBuffer hosturl = request.getRequestURL();
            hosturl.delete(hosturl.length() - request.getRequestURI().length(), hosturl.length()).toString();
            String url = request.getRequestURL().toString();
//            if ("1".equals(Config.map.get("devmodel"))) {
            if (url.contains("http://localhost")) {
                baseUri = hosturl.toString() + "/fuyou/";
            } else {
                baseUri = hosturl.toString() + "/";
            }
            PageSize pageSize = new PageSize(786, 550);
            boolean b = HtmlToPdfUtils.convertToPdf(pageSize, inputStream, baseUri, fontPath, outputStream, content -> {
                Document htmlDom = Jsoup.parse(content);
                htmlDom.selectFirst("#labticketname").html(objTicket.optString("ticketname"));
                htmlDom.selectFirst("#labexamaddr").html(objTicket.optString("examaddr"));
                String examdate = objTicket.optString("examdate");
                String[] arrdate = examdate.split("-");
                htmlDom.selectFirst("#labexamdate").html(arrdate[0] + '年' + arrdate[1] + '月' + arrdate[2] + '日');
                htmlDom.selectFirst("#labexamtime").html(objTicket.optString("examtime"));
                String tip = objTicket.optString("tip");
                String[] arrtip = tip.split("\n");
                String strtip = "";
                for (int i = 0; i < arrtip.length; i++) {
                    strtip += "<p>" + arrtip[i] + "</p>";
                }
                htmlDom.selectFirst("#labtip").html(strtip);
                htmlDom.selectFirst("#labenrollname").html(objEnroll.optString("enrollname"));
                LocalDateTime now = LocalDateTime.now();
                int year = now.getYear();
                htmlDom.selectFirst("#labexamno").html(year + "" + objEnroll.optInt("id"));
                content = htmlDom.toString();
                return content;
            });
            log.info("转换结束，耗时：" + (System.currentTimeMillis() - startTime) + "ms");
            if (!b) {//转换失败
                log.info(enrollName + "转换失败，发放准考证失败");
                throw new Exception("转换失败");
            }
            File pdfFile = new File(localPdfPath);
            if (!pdfFile.exists()) {
                log.info(enrollName + "转换的pdf文件不存在，发放准考证失败");
                throw new Exception("转换的pdf文件不存在，发放准考证失败，请重试");
            }
            boolean islocal = true;
            if (islocal) {
                ticketPath = "userfiles/" + midDir + "/" + ticketfilename;
            } else {
                String osspath = midDir + "/" + ticketfilename;
                pdfIn = new FileInputStream(pdfFile);
                OSSUtil ossUtil = SpringUtils.getBean(OSSUtil.class);
                ossUtil.uploadImg(pdfIn, osspath);
                pdfFile.delete();
                ticketPath = osspath;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (StringUtil.notNull(e.getMessage()) && e.getMessage().indexOf("失败") > -1) {//为主动抛出的异常
                throw new Exception(e.getMessage());
            } else {
                throw new Exception("系统错误，发放准考证失败");
            }
        } finally {
            FileUtil.close(inputStream, outputStream, pdfIn);
        }
        return ticketPath;
    }

    /**
     * 下载准考证
     *
     * @param params
     * @param request
     * @return
     */
    public static String downloadticketPdf(JSONObject params, HttpServletRequest request) {
        log.info("downloadPdf");
        String uploadPath = Global.ProjectPath + "upload";
        String baseFile = "ticket_" + SecurityUtils.getAttribute("uid");
        String basePath = uploadPath + File.separator + baseFile;
        File baseDir = new File(basePath);
        if (!baseDir.exists()) {//文件夹不存在，则生成
            baseDir.mkdirs();
        }
        String strIds = params.optString("ids");
        String enrollSql = "select e.id,noticeid,areacode,areaname,yeyid,yeyname,enrollemployeeid,enrollname,enrollsex,to_char(enrollbirthday,'YYYY-MM-DD') as enrollbirthday,enrollidcard,enrolleducation,enrollfromgraduate,enrollsubject,enrollmobile,enrollphotopath,enrolleducationphoto,enrolljobphoto,enrollmark,enrollstatus,fromid,frommobile,fromname,fromopenid,fromtype,ticketid,ticketpath,t.ticketname from tb_noticeenroll as e left join tb_noticeticket as t on e.ticketid = t.id where e.id in (" + strIds + ") and ticketid is not null and ticketid!=0";

        JSONArray arrEnroll = DBUtil.selectArray("fyyypt", "", enrollSql);
        String downIds = "0";//已下载的id
        for (int i = 0; i < arrEnroll.length(); i++) {
            JSONObject objEnroll = arrEnroll.optJSONObject(i);
            String areaname = objEnroll.optString("areaname");
            String ticketname = objEnroll.optString("ticketname");
            String dirPath = basePath + File.separator + areaname + File.separator + ticketname;
            File dir = new File(dirPath);
            if (!dir.exists()) {//文件夹不存在，则生成
                dir.mkdirs();
            }
            String enrollname = objEnroll.optString("enrollname");
            String enrollmobile = objEnroll.optString("enrollmobile");
            String ticketpath = objEnroll.optString("ticketpath");
            if (StringUtil.isNull(ticketpath)) {
                continue;
            }
            File destFile = new File(dirPath + File.separator + enrollname + "_" + enrollmobile + ".pdf");
            try {
                //下载文件
                boolean islocal = true;
                if (islocal) {
                    String uploadPath2 = Global.ProjectPath;
                    FileUtils.copyFile(new File(uploadPath2 + File.separator + ticketpath), destFile);
                } else {
                    String prefix = "https://" + Config.map.get("OSS_BUCKETNAME") + "." + Config.map.get("OSS_URI");
                    FileUtils.copyURLToFile(new URL(prefix + ticketpath), destFile);
                }
                int enrollId = objEnroll.optInt("id");
                downIds += "," + enrollId;
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        //修改准考证下载次数
        DBUtil.update("fyyypt", "", "tb_noticeenroll", "ticket_downnum = ticket_downnum + 1", " id in (" + downIds + ")");
        //压缩文件夹
        ZIPUtil.compressManyToOne(basePath, basePath + ".zip");
        //删除文件夹
        deleteDir(new File(basePath));
        return new JSONObject().put("path", "/upload/" + baseFile + ".zip").toString();
    }

    /**
     * 发放证书
     *
     * @param json
     * @param request
     * @return
     */
    public static String generateCert(JSONObject json, HttpServletRequest request) {
        log.info("generateCert：发放结业证书。");
        //返回的信息
        JSONArray result = new JSONArray();
        //1、查询要发放证件的人员信息
        String strIds = json.optString("ids");
        String enrollSql = "select id,noticeid,areacode,areaname,yeyid,yeyname,enrollemployeeid,enrollname,enrollsex,to_char(enrollbirthday,'YYYY-MM-DD') as enrollbirthday,enrollidcard,enrolleducation,enrollfromgraduate,enrollsubject,enrollmobile,enrollphotopath,enrolleducationphoto,enrolljobphoto,enrollmark,enrollstatus,fromid,frommobile,fromname,fromopenid,fromtype,certid,forwardenrollid from tb_noticeenroll where id in (" + strIds + ") and certid is not null and certid!=0";
        JSONArray arrEnroll = DBUtil.selectArray("fyyypt", "", enrollSql);
        //证书对象，缓存证书信息，避免重复查询
        JSONObject objCertJSON = new JSONObject();
        for (int i = 0; i < arrEnroll.length(); i++) {
            JSONObject objEnroll = arrEnroll.optJSONObject(i);
            int enrollId = objEnroll.optInt("id");
            int forwardenrollId = objEnroll.optInt("forwardenrollid");//转发通知报名表的id
            String enrollName = objEnroll.optString("enrollname");
            int certid = objEnroll.optInt("certid");
            if (certid == 0) {//没有证书信息
                result.put(enrollName + "没有证书信息");
                log.info(enrollName + "没有证书信息");
                continue;
            }
            //2、根据报名人员信息中的证书id获取证书信息
            JSONObject objCert = new JSONObject();
            if (objCertJSON.has("" + certid)) {//如果证书信息已经存在，则直接取出证书信息
                objCert = objCertJSON.optJSONObject("" + certid);
            } else {
                String certSql = "select id,certificatename,certificatecontent,certificatesample,certificatename_en,certificatecontent_en,certificatetemplet,validday,signdate,signimgurl,signame,certtype from tb_noticecert where id = " + objEnroll.optInt("certid");
                objCert = DBUtil.selectOneJSON("fyyypt", "", certSql);
                objCertJSON.put("" + certid, objCert);
            }
            try {
                String ossPath = certToPdf(objEnroll, objCert, request);
                JSONObject upRe = DBUtil.update("fyyypt", "", "tb_noticeenroll", "certstate = 2,certpath = '" + ossPath + "'", "id = " + enrollId);
                if (forwardenrollId > 0) {
                    DBUtil.update("fyyypt", "", "tb_noticeenroll", "certstate = 2,certpath = '" + ossPath + "'", "id = " + forwardenrollId);
                }
                if (upRe.has("error")) {
                    result.put(enrollName + upRe.optString("error"));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                result.put(enrollName + e.getMessage());
            }
        }
        if (result.length() > 0) {
            return new JSONObject().put("error", result).toString();
        }
        return new JSONObject().put("re", "发证成功").toString();
    }

    /**
     * 生成证书pdf
     *
     * @param objEnroll
     * @param objCert
     * @param request
     * @return
     * @throws Exception
     */
    private static String certToPdf(JSONObject objEnroll, JSONObject objCert, HttpServletRequest request) throws Exception {
        log.info("certToPdf");
        String certpath = "";
        String enrollName = objEnroll.optString("enrollname");
        InputStream inputStream = null;
        OutputStream outputStream = null;
        InputStream pdfIn = null;
        try {
            String sourcePath = FuYouHandler.class.getClassLoader().getResource("templates").getPath();
            String uploadPath = Global.ProjectPath + "userfiles";
            String midDir = "chengdu/fuyou/cert";
            long startTime = System.currentTimeMillis();
            //html文件所在路径
            String templatePath = sourcePath + "/cert/viewcertificat.html";
            String certificatetemplet = objCert.optString("certificatetemplet");
            if ("2".equals(certificatetemplet)) {
                templatePath = sourcePath + "/cert/viewcertificat1.html";
            }
            //pdf文件存储路径
            String idcard = objEnroll.optString("enrollidcard");
            File file = new File(uploadPath + "/" + midDir);
            if (!file.exists()) {
                file.mkdirs();
            }
            String cardfilename = "certificat_" + idcard + "_" + new Date().getTime() + Math.round(Math.random() * 1000) + ".pdf";
            String localPdfPath = uploadPath + "/" + midDir + "/" + cardfilename;
            inputStream = new FileInputStream(templatePath);
            outputStream = new FileOutputStream(localPdfPath);
            //字体文件路劲
            String fontPath = sourcePath + "/healthcard/font/simsun.ttc,0";
            //图片，css等文件的基础路径
            String baseUri = "";
            StringBuffer hosturl = request.getRequestURL();
            hosturl.delete(hosturl.length() - request.getRequestURI().length(), hosturl.length()).toString();
            String url = request.getRequestURL().toString();
//            if ("1".equals(Config.map.get("devmodel"))) {
            if (url.contains("http://localhost")) {
                baseUri = hosturl.toString() + "/fuyou/";
            } else {
                baseUri = hosturl.toString() + "/";
            }
            boolean islocal = true;
            final String baseUri2 = baseUri;
            PageSize pageSize = new PageSize(786, 550);
            boolean b = HtmlToPdfUtils.convertToPdf(pageSize, inputStream, baseUri, fontPath, outputStream, content -> {
                OssConfig ossProp = SpringUtils.getBean(OssConfig.class);
                String prefix = "https://" + ossProp.getOSS_BUCKETNAME() + "." + ossProp.getOSS_URI();
                if (islocal) {//
                    prefix = baseUri2;
                }
                Document htmlDom = Jsoup.parse(content);
                htmlDom.selectFirst("#labcertificatename").html(objCert.optString("certificatename"));
                if ("1".equals(certificatetemplet)) {
                    String certificatecontent = objCert.optString("certificatecontent");
                    certificatecontent = replaceContent(certificatecontent, "username", objEnroll.optString("enrollname"));
                    certificatecontent = replaceContent(certificatecontent, "username_line", "<span class='line_txt1'>" + objEnroll.optString("enrollname") + "</span>");
                    certificatecontent = replaceContent(certificatecontent, "idcard", objEnroll.optString("enrollidcard"));
                    certificatecontent = replaceContent(certificatecontent, "idcard_line", "<span class='line_txt1'>" + objEnroll.optString("enrollidcard") + "</span>");
                    htmlDom.selectFirst("#labcertificatecontent").html(certificatecontent);
                    htmlDom.selectFirst("#signame").html(objCert.optString("signame"));
                } else {
                    htmlDom.selectFirst("#labcertificatename_en").html(objCert.optString("certificatename_en"));
                    htmlDom.selectFirst("#labcertificatecontent").html(objCert.optString("certificatecontent"));
                    htmlDom.selectFirst("#labcertificatecontent_en").html(objCert.optString("certificatecontent_en"));
                    htmlDom.selectFirst("#truename").html(objEnroll.optString("enrollname"));
                    String[] arrsp = PinYinUtil.getOne(objEnroll.optString("enrollname"));
                    htmlDom.selectFirst("#user_sp").html(arrsp[0]);
                    htmlDom.selectFirst("#idcard").html(objEnroll.optString("enrollidcard"));
                }
                htmlDom.selectFirst("#headimg").attr("src", prefix + objEnroll.optString("enrollphotopath") + "?x-oss-process=image/resize,m_fill,w_140");
                String signdate = objCert.optString("signdate");
                String[] signdateArr = signdate.split("-");
                htmlDom.selectFirst("#signdate").html(signdateArr[0] + "年" + signdateArr[1] + "月" + signdateArr[2] + "日");
                htmlDom.selectFirst("#signimgurl").attr("src", prefix + objCert.optString("signimgurl") + "?x-oss-process=image/resize,m_fill,w_270");
                content = htmlDom.toString();
                return content;
            });
            log.info("转换结束，耗时：" + (System.currentTimeMillis() - startTime) + "ms");
            if (!b) {//转换失败
                log.info(enrollName + "转换失败，发放证书失败");
                throw new Exception("转换失败");
            }
            File pdfFile = new File(localPdfPath);
            if (!pdfFile.exists()) {
                log.info(enrollName + "转换的pdf文件不存在，发放证书失败");
                throw new Exception("转换的pdf文件不存在，发放证书失败，请重试");
            }
            if (islocal) {
                certpath = "userfiles/" + midDir + "/" + cardfilename;
            } else {
                String osspath = midDir + "/" + cardfilename;
                pdfIn = new FileInputStream(pdfFile);
                OSSUtil ossUtil = SpringUtils.getBean(OSSUtil.class);
                ossUtil.uploadImg(pdfIn, osspath);
                pdfFile.delete();
                certpath = osspath;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (StringUtil.notNull(e.getMessage()) && e.getMessage().indexOf("失败") > -1) {//为主动抛出的异常
                throw new Exception(e.getMessage());
            } else {
                throw new Exception("系统错误，发放证件失败");
            }
        } finally {
            FileUtil.close(inputStream, outputStream, pdfIn);
        }
        return certpath;
    }

    /**
     * 下载证书
     *
     * @param params
     * @param request
     * @return
     */
    public static String downloadCertPdf(JSONObject params, HttpServletRequest request) {
        log.info("downloadPdf");
        String uploadPath = Global.ProjectPath + "upload";
        String baseFile = "cert_" + SecurityUtils.getAttribute("uid");
        String basePath = uploadPath + File.separator + baseFile;
        File baseDir = new File(basePath);
        if (!baseDir.exists()) {//文件夹不存在，则生成
            baseDir.mkdirs();
        }
        String strIds = params.optString("ids");
        String enrollSql = "select e.id,noticeid,areacode,areaname,yeyid,yeyname,enrollemployeeid,enrollname,enrollsex,to_char(enrollbirthday,'YYYY-MM-DD') as enrollbirthday,enrollidcard,enrolleducation,enrollfromgraduate,enrollsubject,enrollmobile,enrollphotopath,enrolleducationphoto,enrolljobphoto,enrollmark,enrollstatus,fromid,frommobile,fromname,fromopenid,fromtype,certid,certpath,c.certificatename from tb_noticeenroll as e left join tb_noticecert as c on e.certid = c.id where e.id in (" + strIds + ") and certid is not null and certid!=0";
        JSONArray arrEnroll = DBUtil.selectArray("fyyypt", "", enrollSql);
        String downIds = "0";//已下载的id
        for (int i = 0; i < arrEnroll.length(); i++) {
            JSONObject objEnroll = arrEnroll.optJSONObject(i);
            String areaname = objEnroll.optString("areaname");
            String certificatename = objEnroll.optString("certificatename");
            String dirPath = basePath + File.separator + areaname + File.separator + certificatename;
            File dir = new File(dirPath);
            if (!dir.exists()) {//文件夹不存在，则生成
                dir.mkdirs();
            }
            String enrollname = objEnroll.optString("enrollname");
            String enrollmobile = objEnroll.optString("enrollmobile");
            String certpath = objEnroll.optString("certpath");
            if (StringUtil.isNull(certpath)) {
                continue;
            }
            File destFile = new File(dirPath + File.separator + enrollname + "_" + enrollmobile + ".pdf");
            try {
                //下载文件
                boolean islocal = true;
                if (islocal) {
                    String uploadPath2 = Global.ProjectPath;
                    FileUtils.copyFile(new File(uploadPath2 + File.separator + certpath), destFile);
                } else {
                    String prefix = "https://" + Config.map.get("OSS_BUCKETNAME") + "." + Config.map.get("OSS_URI");
                    FileUtils.copyURLToFile(new URL(prefix + certpath), destFile);
                }
                int enrollId = objEnroll.optInt("id");
                downIds += "," + enrollId;
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        //修改准考证下载次数
        DBUtil.update("fyyypt", "", "tb_noticeenroll", "cert_downnum = cert_downnum + 1", " id in (" + downIds + ")");
        //压缩文件夹
        ZIPUtil.compressManyToOne(basePath, basePath + ".zip");
        //删除文件夹
        deleteDir(new File(basePath));
        return new JSONObject().put("path", "/upload/" + baseFile + ".zip").toString();
    }

    /**
     * 内容替换方法
     *
     * @param template
     * @param key
     * @param val
     * @return
     */
    private static String replaceContent(String template, String key, String val) {
        String pattern = "\\$\\{(.+?)\\}";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(template);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            if (key.equals(m.group(1))) {
                m.appendReplacement(sb, val);
                break;
            }
        }
        m.appendTail(sb);
        return sb.toString();
    }

    private static boolean deleteDir(File dir) {
        File[] files = dir.listFiles();
        for (int i = 0; i < files.length; i++) {
            if (files[i].isDirectory()) {
                deleteDir(files[i]);
            } else {
                files[i].delete();
            }
        }
        return dir.delete();
    }

    private static void delayDeleteFile(String filePath, long offTime) {
        File file = new File(filePath);
        if (!file.exists()) {
            return;
        }
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        try {
            executor.schedule(new Runnable() {
                @Override
                public void run() {
                    file.delete();
                }
            }, offTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 导出幼儿园
     *
     * @param param
     * @param request
     * @return
     */
    public static String exportExcelYey(JSONObject param, HttpServletRequest request) {
        log.info("exportExcelYey");
        JSONObject objre = new JSONObject();
        String sql = "select yey.id,yeyname,areaname1,relationstatus,to_char(relationtime,'YYYY-MM-DD HH24:MI:SS') as relationtime,ptid,areacode1,guid,fuyouorganid,fuyouorganname,fuyoumdicalorganid,fuyoumdicalorganname,affiliate,isopen, yey.sp, yey.spe, legalperson, yey.phone, yey.areacode, yey.areaname, yey.address, yey.lng, yey.lat, classification, dbid,  bmapaddr, email, yeytype, affiliation, to_char(evaluatedate,'YYYY-MM-DD') as evaluatedate, isbeian, feetype, memberfee, freemsg, pwd as password,userrole,w_systemright_id,fuyouguid,c.id as companyid,shstatus from yey left join tb_company as c on c.yeyid=yey.id and c.isdel=0 where  yey.isdel=0 and (relationstatus='yes' or ptid is null)";
        if (StringUtil.notNull(param.optString("organid"))) {
            sql += " and fuyouorganid = " + param.optString("organid");
        }
        if (StringUtil.notNull(param.optString("yeyname"))) {
            sql += "and yeyname like '%" + param.optString("yeyname") + "%'";
        }
        if (StringUtil.notNull(param.optString("yeytype"))) {
            sql += " and yeytype = " + param.optString("yeytype");
        }
        if (StringUtil.notNull(param.optString("selclassify"))) {
            sql += " and classification = " + param.optString("selclassify");
        }
        if (StringUtil.notNull(param.optString("fuyouorganname"))) {
            sql += " and (fuyouorganname like '%" + param.optString("fuyouorganname") + "%' or fuyoumdicalorganname like '%" + param.optString("fuyouorganname") + "%')";
        }
        sql += " order by yey.id";
        String guid = (String) SecurityUtils.getAttribute("guid");
        JSONArray arrYey = DBUtil.selectArray("f", guid, sql);
        HSSFWorkbook wb = null;
        FileOutputStream fout = null;
        try {
            // 第一步，创建一个webbook，对应一个Excel文件
            wb = new HSSFWorkbook();
            HSSFSheet sheet = null;
            // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
            sheet = wb.createSheet();
//        sheet.setColumnWidth(0, 25000);
            HSSFCellStyle cellStyle = getCellStyle(wb);
            // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
            HSSFRow row = sheet.createRow(0);
            // 第四步，创建单元格，并设置值表头 设置表头居中
            HSSFCell cell1 = row.createCell(0);
            cell1.setCellValue("托幼机构名称");
            HSSFCell cell2 = row.createCell(1);
            cell2.setCellValue("所属地区");
            HSSFCell cell3 = row.createCell(2);
            cell3.setCellValue("托幼机构地址");
            HSSFCell cell4 = row.createCell(3);
            cell4.setCellValue("托幼机构类型");
            HSSFCell cell5 = row.createCell(4);
            cell5.setCellValue("机构所属关系");
            HSSFCell cell6 = row.createCell(5);
            cell6.setCellValue("幼儿园等级");
            HSSFCell cell7 = row.createCell(6);
            cell7.setCellValue("通过招生前评价时间");
            HSSFCell cell8 = row.createCell(7);
            cell8.setCellValue("是否通过教育部门或卫生行政部门备案");
//            HSSFCell cell9 = row.createCell(8);
//            cell9.setCellValue("联系人");
//            HSSFCell cell10 = row.createCell(9);
//            cell10.setCellValue("联系电话");
            HSSFCell cell11 = row.createCell(8);
            cell11.setCellValue("卫生服务中心");
            HSSFCell cell12 = row.createCell(9);
            cell12.setCellValue("体检医院名称");
            String[] arrAffiliation = new String[]{"", "教育部门办园", "其他部门办园", "事业单位", "部队幼儿园", "集体办园", "地方企业", "民办幼儿园"};
            String[] arrClassify = new String[]{"", "一级", "", "", "二级", "", "", "三级"};
            for (int i = 0; i < arrYey.length(); i++) {
                JSONObject objYey = arrYey.optJSONObject(i);
                HSSFRow contentRow = sheet.createRow(i + 1);
                // 第五步，创建单元格并填充数据
                //yey.id,yeyname,areaname1,relationstatus,to_char(relationtime,'YYYY-MM-DD HH24:MI:SS') as relationtime,ptid,areacode1,guid,fuyouorganid,fuyouorganname,fuyoumdicalorganid,fuyoumdicalorganname,affiliate,isopen, yey.sp, yey.spe, legalperson, yey.phone, yey.areacode, yey.areaname, yey.address, yey.lng, yey.lat, classification, dbid,  bmapaddr, email, yeytype, affiliation, evaluatedate, isbeian, isbeian, feetype, memberfee, freemsg, pwd as password,userrole,w_systemright_id,fuyouguid,c.id as companyid,shstatus
                HSSFCell contentCell1 = contentRow.createCell(0);
                contentCell1.setCellValue(objYey.optString("yeyname"));
                HSSFCell contentCell2 = contentRow.createCell(1);
                contentCell2.setCellValue(objYey.optString("areaname"));
                HSSFCell contentCell3 = contentRow.createCell(2);
                contentCell3.setCellValue(objYey.optString("address"));
                HSSFCell contentCell4 = contentRow.createCell(3);
                int yeytype = objYey.optInt("yeytype");
                contentCell4.setCellValue(yeytype == 1 ? "幼儿园" : yeytype == 3 ? "托育机构" : "托幼一体园");
                HSSFCell contentCell5 = contentRow.createCell(4);
                contentCell5.setCellValue(arrAffiliation[objYey.optInt("affiliation")]);
                HSSFCell contentCell6 = contentRow.createCell(5);
                contentCell6.setCellValue(arrClassify[objYey.optInt("classification")]);
                HSSFCell contentCell7 = contentRow.createCell(6);
                contentCell7.setCellValue(objYey.optString("evaluatedate"));
                HSSFCell contentCell8 = contentRow.createCell(7);
                contentCell8.setCellValue(objYey.optInt("isbeian") == 1 ? "是" : "否");
//                HSSFCell contentCell9 = contentRow.createCell(8);
//                contentCell9.setCellValue(objYey.optString("legalperson"));
//                HSSFCell contentCell10 = contentRow.createCell(9);
//                contentCell10.setCellValue(objYey.optString("phone"));
                HSSFCell contentCell11 = contentRow.createCell(8);
                contentCell11.setCellValue(objYey.optString("fuyouorganname"));
                HSSFCell contentCell12 = contentRow.createCell(9);
                contentCell12.setCellValue(objYey.optString("fuyoumdicalorganname"));
            }

            Date date = new Date();
            DateFormat format1 = new SimpleDateFormat("yyyyMMddHHmmss");
            Random r = new Random();
            int urlrandom = r.nextInt(1000);
            String eurl = format1.format(date) + urlrandom + ".xls";
            fout = new FileOutputStream(Global.ProjectPath + "upload/" + eurl);
            wb.write(fout);
            close(fout);
            wb.close();
            objre.put("path", "upload/" + eurl);
        } catch (Exception e) {
            objre.put("error", "生成excel出错");
            log.error("生成excel出错", e);
        } finally {
            FileUtil.close(fout, wb);
        }
        return objre.toString();
    }

    /**
     * 导出体检名单
     *
     * @param param
     * @param request
     * @return
     */
    public static String exportExcelCheckResult(JSONObject param, JSONObject param2, HttpServletRequest request) {
        log.info("exportExcelCheckResult");
        JSONObject objre2 = SendFySMS.checksms(param2);
        if (objre2.has("error")) {
            return objre2.toString();
        }
        JSONObject objre = new JSONObject();
        int checkid = param.optInt("checkid");
        int yeyid = param.optInt("yeyid");
        String guid = param.optString("guid");
        String outpath = exportByYey(checkid, yeyid, guid, request);
        if (StringUtil.notNull(outpath)) {
            objre.put("path", outpath);
        } else {
            objre.put("error", "生成excel出错");
        }
        return objre.toString();
    }

    private static String exportByYey(int checkid, int yeyid, String yguid, HttpServletRequest request) {
        String guid = (String) SecurityUtils.getAttribute("guid");
        String sqlyey = "select sortway,id from yey where isdel=0 and id = " + yeyid;
        JSONObject jsonObject = DBUtil.selectOneJSON("f", yguid, sqlyey);
        int sortway = jsonObject.optInt("sortway");
        String orderField = " order by classno,sex desc, birthday,stuno";
        if (sortway == 1) {
            orderField = " order by classno,sex desc, birthday,stuno";
        } else if (sortway == 3) {
            orderField = " order by classno,birthday,stuno";
        } else if (sortway == 4) {
            orderField = " order by classno,stusort,stuno";
        }

        String sql = "select id,check_id,package_id,yeyid,yeyname,classno,claname,stuno,stuname,sex,to_char(birthday,'YYYY-MM-DD') as birthday,age,credentialsnum from check_child_result where isdel = 0 and check_id = " + checkid + " and yeyid = " + yeyid + " and fyguid = '" + yguid + "'" + orderField;
        JSONArray arrResult = DBUtil.selectArray("f", guid, sql);
        String standSql = "select sysvalue from ek_system where isdel = 0 and sysno='eyestand'";
        JSONObject objStand = DBUtil.selectOneJSON("f", guid, standSql);
        int eyestand = objStand.has("sysvalue") ? objStand.optInt("sysvalue") : 1;
        FileInputStream fis = null;
        FileOutputStream out = null;
        XSSFWorkbook wb = null;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssSSSS");
            String strtm = format.format(new Date());
            Random r = new Random();
            int urlrandom = r.nextInt(1000);
            String outdir = "upload/check";
            File outDir = new File(Global.ProjectPath + outdir);
            if (!outDir.exists()) {
                outDir.mkdirs();
            }
            String outpath = outdir + "/result_" + strtm + urlrandom + ".xlsx";
//            if(!"".equals(dirpath)){//如果有指定的保存路径，则使用指定的保存路径
//                outpath = "userfiles" + File.separator + "smarttable" + File.separator + dirpath;
//            }
            String relativePathOut = Global.ProjectPath + outpath;//导出路径
            String sourcePath = FuYouHandler.class.getClassLoader().getResource("templates").getPath();

            String inpath = sourcePath + File.separator + "check" + File.separator + "checkresult" + eyestand + ".xlsx";//空白文件
            String relativePathIn = inpath;//获取文件路径
            fis = new FileInputStream(relativePathIn);
            wb = new XSSFWorkbook(fis);
            XSSFSheet sheet = wb.getSheetAt(0);
            for (int i = 0; i < arrResult.length(); i++) {
                JSONObject objResult = arrResult.optJSONObject(i);
                XSSFRow contentRow = sheet.createRow(i + 1);
                // 创建单元格并填充数据
                //id,check_id,package_id,yeyid,yeyname,classno,claname,stuno,stuname,sex,birthday,age
                XSSFCell contentCell0 = contentRow.createCell(0);
                contentCell0.setCellValue(objResult.optInt("id"));
                XSSFCell contentCell1 = contentRow.createCell(1);
                contentCell1.setCellValue(objResult.optString("yeyname"));
                XSSFCell contentCell2 = contentRow.createCell(2);
                contentCell2.setCellValue(objResult.optString("claname"));
                XSSFCell contentCell3 = contentRow.createCell(3);
                contentCell3.setCellValue(objResult.optString("stuname"));
                XSSFCell contentCell4 = contentRow.createCell(4);
                contentCell4.setCellValue(objResult.optString("sex"));
                XSSFCell contentCell5 = contentRow.createCell(5);
                contentCell5.setCellValue(getZhAgeByAge(objResult.optString("age")));
                XSSFCell contentCell6 = contentRow.createCell(6);
                contentCell6.setCellValue(objResult.optString("birthday"));
                XSSFCell contentCell7 = contentRow.createCell(7);
                contentCell7.setCellValue(objResult.optString("credentialsnum"));
                XSSFCell contentCell10 = contentRow.createCell(10);
                contentCell10.setCellValue("立位");
                XSSFCell contentCell13 = contentRow.createCell(13);
                contentCell13.setCellValue("未见异常");
                XSSFCell contentCell14 = contentRow.createCell(14);
                contentCell14.setCellValue("未见异常");
                XSSFCell contentCell116 = contentRow.createCell(16);
                contentCell116.setCellValue("通过");
                XSSFCell contentCell17 = contentRow.createCell(17);
                contentCell17.setCellValue("通过");
                XSSFCell contentCell28 = contentRow.createCell(28);
                contentCell28.setCellValue("正常");
                XSSFCell contentCell29 = contentRow.createCell(29);
                contentCell29.setCellValue("正常");
                XSSFCell contentCell33 = contentRow.createCell(33);
                contentCell33.setCellValue("未见异常");
            }
            out = new FileOutputStream(relativePathOut);
            wb.write(out);
            close(out);
            wb.close();
            log.info("生成excel成功，outpath：" + outpath);
            return outpath;
        } catch (Exception e) {
            log.error("生成excel出错", e);
            return null;
        } finally {
            FileUtil.close(fis, out, wb);
        }
    }

    private static String getZhAgeByAge(String age) {
        String strage = "";
        if (StringUtil.notNull(age)) {
            String[] arrage = (age + "").split("\\.", 2);
            strage = arrage.length > 0 ? arrage[0] + "岁" + (StringUtil.notNull(arrage[1]) ? Integer.valueOf(arrage[1]) + "月" : "") : "";
        }
        return strage;
    }

    private static String getAgeByZhAge(String age) {
        String strage = "";
        if (StringUtil.notNull(age)) {
            strage = age.substring(0, age.indexOf("岁"));
            if (age.contains("月")) {
                String month = age.substring(age.indexOf("岁") + 1, age.indexOf("月"));
                int m = Integer.parseInt(month);
                strage += "." + (m > 9 ? m : "0" + m);
            }
        }
        return strage;
    }
    public static boolean hasEmptyButNotAllEmpty(String... strings) {
        boolean hasEmpty = false;
        boolean hasNonEmpty = false;

        for (String str : strings) {
            if (StringUtil.isNull(str)) {
                hasEmpty = true;
            } else {
                hasNonEmpty = true;
            }
            // 提前终止条件：已经确定满足条件
            if (hasEmpty && hasNonEmpty) {
                return true;
            }
        }
        return false;
    }
    /**
     * 导入体检数据
     *
     * @param param
     * @param request
     * @return
     */
    public static String importExcelCheckResult(JSONObject param, HttpServletRequest request) {
        log.info("importExcelCheckResult");
        JSONObject objre = new JSONObject();
        int checkid = param.optInt("checkid");
        String filepath = param.optString("filepath");
        String strcol = param.optString("strcol");
        strcol = strcol + ",";
        String[][] arr = getDatafromExcel(filepath, 0, 1);
        JSONArray arrError = new JSONArray();
        if (arr != null) {
            for (int i = 0; i < arr.length; i++) {
                if (StringUtil.isNull(arr[i][0])) {//没有id
                    continue;
                }
                int id = (int) Math.round(Double.valueOf(arr[i][0]));
                String sex = arr[i][4];
                String birthday = arr[i][6];
                String age = arr[i][5];
                String idcard = arr[i][7];
                String hight = arr[i][8];
                String weight = arr[i][9];
                String hest = arr[i][10];//StringUtil.isNull(arr[i][8]) ? null : "卧位".equals(arr[i][8]) ? "1" : "2";
                String hw_doctor = arr[i][11];
                String rday = arr[i][12];


                if(strcol.contains("hight,")){
                    if(hasEmptyButNotAllEmpty(hight, weight, rday)){
                        arrError.put("第" + (i + 2) + "行，身高体重相关数据中有空值，未保存");
                        continue;
                    }
                    if(StringUtil.isNull(hest) && StringUtil.notNull(hight) && StringUtil.notNull(weight) && StringUtil.notNull(rday)){
                        arrError.put("第" + (i + 2) + "行，卧立位空值，未保存");
                        continue;
                    }
                }

                //String leye = arr[i][13];
                //String reye = arr[i][14];
                //String eye_doctor = arr[i][13];
                //String eyecheckdate = arr[i][14];
                String llis_show = arr[i][13];
                String rlis_show = arr[i][14];
                String llis_show_beizhu = arr[i][15];

                String llis = arr[i][16];//StringUtil.isNull(arr[i][15]) ? null : "通过".equals(arr[i][15]) ? "1" : "2";
                String rlis = arr[i][17];//StringUtil.isNull(arr[i][16]) ? null : "通过".equals(arr[i][16]) ? "1" : "2";
                String llis_beizhu = arr[i][18];
                String tlscff = arr[i][19];//StringUtil.isNull(arr[i][17]) ? null : "听觉评估仪".equals(arr[i][17]) ? "1" : "2";//筛查方法
                String oae = null;
                if (arr[i][19].contains("瞬态")) {
                    oae = "1";
                } else if (arr[i][19].contains("畸变")) {
                    oae = "2";
                }
                String lis_doctor = arr[i][20];
                String lis_ckdate = arr[i][21];

                String hematin = arr[i][22];
                String hematin_doctor = arr[i][23];
                String hematin_ckdate = arr[i][24];
                if(strcol.contains("hematin,") && hasEmptyButNotAllEmpty(hematin, hematin_ckdate)){
                    arrError.put("第" + (i + 2) + "行，血红蛋白相关数据中有空值，未保存");
                    continue;
                }

                String carnum = arr[i][25];//牙齿总数
                //String car2 = arr[i][25];//矫正颗数
                //String quchi = arr[i][26];//龋齿颗数
                //int car1 = 0;//检出颗数
                String op_doctor = arr[i][26];//体检医生
                String cardate = arr[i][27];//检查日期

                String yan = arr[i][28];//左眼外观
                String yan_wgr = arr[i][29];//右眼外观
                String yan_content = arr[i][30];//备注
                String yan_l = arr[i][31];//左眼
                String yan_r = arr[i][32];//右眼
                String yan_scjg = arr[i][33];//筛查结果
                String yan_lse = arr[i][34];//左SE
                String yan_lds = arr[i][35];//左DS
                String yan_ldc = arr[i][36];//左DC
                String yan_la = arr[i][37];//左A
                String qgjg_l = arr[i][38];//左眼屈光结果
                String yan_rse = arr[i][39];//右SE
                String yan_rds = arr[i][40];//右DS
                String yan_rdc = arr[i][41];//右DC
                String yan_ra = arr[i][42];//右A
                String qgjg_r = arr[i][43];//右眼屈光结果
                String yan_doctor = arr[i][44];//体检医生
                String yan_ckdate = arr[i][45];//检查日期
                if(strcol.contains("yan_l,") && hasEmptyButNotAllEmpty(yan_l, yan_r, yan_ckdate)){
                    arrError.put("第" + (i + 2) + "行，视力相关数据中有空值，未保存");
                    continue;
                }

                String tablename = "check_child_result";
                String columns = "";
                String values = "";
                if (StringUtil.notNull(hight) && strcol.contains("hight,")) {
                    columns += ",hight";
                    values += "," + hight;
                }
                if (StringUtil.notNull(weight) && strcol.contains("weight,")) {
                    columns += ",weight";
                    values += "," + weight;
                }
                if (StringUtil.notNull(hest) && strcol.contains("hest,")) {
                    columns += ",hest";
                    values += "," + ("卧位".equals(hest) ? "1" : "2");
                }
                if (StringUtil.notNull(hw_doctor) && strcol.contains("hw_doctor,")) {
                    columns += ",hw_doctor";
                    values += ",'" + hw_doctor + "'";
                }
                if (StringUtil.notNull(rday) && strcol.contains("rday,")) {
                    columns += ",rday";
                    values += ",'" + rday + "'";
                }
                if (StringUtil.notNull(yan_l) && strcol.contains("yan_l,")) {
                    columns += ",leye";
                    values += ",'" + yan_l + "'";
                }
                if (StringUtil.notNull(yan_r) && strcol.contains("yan_r,")) {
                    columns += ",reye";
                    values += ",'" + yan_r + "'";
                }
                if (StringUtil.notNull(yan_doctor) && strcol.contains("yan_doctor,")) {
                    columns += ",eye_doctor";
                    values += ",'" + yan_doctor + "'";
                }
                if (StringUtil.notNull(yan_ckdate) && strcol.contains("yan_ckdate,")) {
                    columns += ",eyecheckdate";
                    values += ",'" + yan_ckdate + "'";
                }
                if (StringUtil.notNull(llis_show) && strcol.contains("llis_show,")) {
                    columns += ",llis_show";
                    values += "," + ("未见异常".equals(llis_show) ? "1" : "2");
                }
                if (StringUtil.notNull(rlis_show) && strcol.contains("rlis_show,")) {
                    columns += ",rlis_show";
                    values += "," + ("未见异常".equals(rlis_show) ? "1" : "2");
                }
                if (StringUtil.notNull(llis_show_beizhu) && strcol.contains("llis_show_beizhu,")) {
                    columns += ",llis_show_beizhu";
                    values += ",'"+llis_show_beizhu+"'";
                }
                if (StringUtil.notNull(llis) && strcol.contains("llis,")) {
                    columns += ",llis";
                    values += "," + ("通过".equals(llis) ? "1" :"未通过".equals(llis)?2: "未检查".equals(llis)?"3":"其他".equals(llis)?4:0);
                }
                if (StringUtil.notNull(rlis) && strcol.contains("rlis,")) {
                    columns += ",rlis";
                    values +=  "," + ("通过".equals(rlis) ? "1" :"未通过".equals(rlis)?2: "未检查".equals(rlis)?"3":"其他".equals(rlis)?4:0);
                }
                if (StringUtil.notNull(llis_beizhu) && strcol.contains("llis_beizhu,")) {
                    columns += ",llis_beizhu";
                    values += ",'"+llis_beizhu+"'";
                }
                if (StringUtil.notNull(tlscff) && strcol.contains("tlscff,")) {
                    columns += ",tlscff";
                    values += "," + ("听觉评估仪".equals(tlscff) ? "1" : "2");
                }
                if (StringUtil.notNull(tlscff) && tlscff.contains("OAE耳声发射") && strcol.contains("tlscff,")) {
                    columns += ",oae";
                    values += "," + oae;
                }
                if (StringUtil.notNull(lis_doctor) && strcol.contains("lis_doctor,")) {
                    columns += ",lis_doctor";
                    values += ",'" + lis_doctor + "'";
                }
                if (StringUtil.notNull(lis_ckdate) && strcol.contains("lis_ckdate,")) {
                    columns += ",lis_ckdate";
                    values += ",'" + lis_ckdate + "'";
                }
                if (StringUtil.notNull(hematin) && strcol.contains("hematin,")) {
                    columns += ",hematin";
                    values += "," + hematin;
                }
                if (StringUtil.notNull(hematin_doctor) && strcol.contains("hematin_doctor,")) {
                    columns += ",hematin_doctor";
                    values += ",'" + hematin_doctor + "'";
                }
                if (StringUtil.notNull(hematin_ckdate) && strcol.contains("hematin_ckdate,")) {
                    columns += ",hematin_ckdate";
                    values += ",'" + hematin_ckdate + "'";
                }
                if (StringUtil.notNull(carnum) && strcol.contains("carnum,")) {
                    columns += ",carnum";
                    values += "," + carnum;
                }
                //if (StringUtil.notNull(car2)) {
                //    columns += ",car2";
                //    values += "," + car2;
                //    car1 += Double.parseDouble(car2);
                //}
                //if (StringUtil.notNull(quchi)) {
                //    car1 += Double.parseDouble(quchi);
                //}
                //columns += ",car1";
                //values += "," + car1;
                if (StringUtil.notNull(op_doctor) && strcol.contains("op_doctor,")) {
                    columns += ",op_doctor,kq_doctor";
                    values += ",'" + op_doctor + "','"+op_doctor+"'";
                }
                if (StringUtil.notNull(cardate) && strcol.contains("cardate,")) {
                    columns += ",cardate,kq_ckdate";
                    values += ",'" + cardate + "','" + cardate + "'";
                }
                if (StringUtil.notNull(yan) && strcol.contains("yan,")) {
                    columns += ",yan";
                    values += "," + ("正常".equals(yan) ? "1" : "异常".equals(yan) ? "2" : "3");
                }
                if (StringUtil.notNull(yan_wgr) && strcol.contains("yan_wgr,")) {
                    columns += ",yan_wgr";
                    values += "," + ("正常".equals(yan_wgr) ? "1" : "异常".equals(yan_wgr) ? "2" : "3");
                }
                if (StringUtil.notNull(yan_content) && strcol.contains("yan_content,")) {
                    columns += ",yan_content";
                    values += ",'" + yan_content + "'";
                }
                if (StringUtil.notNull(yan_l) && strcol.contains("yan_l,")) {
                    columns += ",yan_l";
                    values += ",'" + yan_l + "'";
                }
                if (StringUtil.notNull(yan_r) && strcol.contains("yan_r,")) {
                    columns += ",yan_r";
                    values += ",'" + yan_r + "'";
                }
                if (StringUtil.notNull(yan_scjg) && strcol.contains("yan_scjg,")) {
                    columns += ",yan_scjg";
                    values += "," + ("未见异常".equals(yan_scjg) ? 1 : "异常".equals(yan_scjg) ? 2 : "未检查".equals(yan_scjg) ? 3 : 4);
                }
                if (StringUtil.notNull(yan_lse) && strcol.contains("yan_lse,")) {
                    columns += ",yan_lse";
                    values += ",'" + yan_lse + "'";
                }
                if (StringUtil.notNull(yan_lds) && strcol.contains("yan_lds,")) {
                    columns += ",yan_lds";
                    values += ",'" + yan_lds + "'";
                }
                if (StringUtil.notNull(yan_ldc) && strcol.contains("yan_ldc,")) {
                    columns += ",yan_ldc";
                    values += ",'" + yan_ldc + "'";
                }
                if (StringUtil.notNull(yan_la) && strcol.contains("yan_la,")) {
                    columns += ",yan_la";
                    values += ",'" + yan_la + "'";
                }
                if (StringUtil.notNull(yan_rse) && strcol.contains("yan_rse,")) {
                    columns += ",yan_rse";
                    values += ",'" + yan_rse + "'";
                }
                if (StringUtil.notNull(yan_rds) && strcol.contains("yan_rds,")) {
                    columns += ",yan_rds";
                    values += ",'" + yan_rds + "'";
                }
                if (StringUtil.notNull(yan_rdc) && strcol.contains("yan_rdc,")) {
                    columns += ",yan_rdc";
                    values += ",'" + yan_rdc + "'";
                }
                if (StringUtil.notNull(yan_ra) && strcol.contains("yan_ra,")) {
                    columns += ",yan_ra";
                    values += ",'" + yan_ra + "'";
                }
                if (StringUtil.notNull(yan_ckdate) && strcol.contains("yan_ckdate,")) {
                    columns += ",yan_ckdate";
                    values += ",'" + yan_ckdate + "'";
                }
                if (StringUtil.notNull(yan_doctor) && strcol.contains("yan_doctor,")) {
                    columns += ",yan_doctor";
                    values += ",'" + yan_doctor + "'";
                }
                String guid = (String) SecurityUtils.getAttribute("guid");
                if (columns.length() > 0) {//需要更新的列数为0
                    columns = columns.substring(1);
                    values = values.substring(1);
                    String wheres = "id = " + id;
                    JSONObject objstu = new JSONObject();
                    objstu.put("sex", sex);
                    objstu.put("birthday", birthday);
                    objstu.put("age", getAgeByZhAge(age));
                    objstu.put("tjid", id);
                    String re = CheckSaveHandler.editChildResult(new String[]{"", tablename, columns, values, wheres, objstu.toString()}, new String[]{"f", guid}, request);
                    JSONObject json = new JSONObject(re);
                    if (json.has("error")) {//保存失败
                        arrError.put("第" + (i + 2) + "行数据保存异常，" + json.optString("error"));
                    }
                }

                String tablename2 = "check_child_result_eye";
                String columns2 = "";
                String values2 = "";
                if (StringUtil.notNull(qgjg_l) && strcol.contains("qgjg_l,")) {
                    columns2 += ",qgjg_l";
                    values2 += "," + ("未检查".equals(qgjg_l) ? 1 : "正常".equals(qgjg_l) ? 2 : 3);
                }
                if (StringUtil.notNull(qgjg_r) && strcol.contains("qgjg_r,")) {
                    columns2 += ",qgjg_r";
                    values2 += "," + ("未检查".equals(qgjg_r) ? 1 : "正常".equals(qgjg_r) ? 2 : 3);
                }
                if(columns2.length() > 0){
                    columns2 += ",age";
                    values2 += "," + getAgeByZhAge(age);
                    columns2 += ",result_id";
                    values2 += "," + id;
                    columns2 = columns2.substring(1);
                    values2 = values2.substring(1);
                    DBUtil.insert("f", guid, tablename2, columns2, values2, " isdel = 0 and result_id = " + id);
                }
            }
        }
        if (arrError.length() == 0) {
            objre.put("re", "导入成功");
        } else {
            objre.put("error", arrError.toString());
        }
        return objre.toString();
    }

    /**
     * 得到Cell内容
     *
     * @param cell
     * @return
     */
    public static String getcellValue(Cell cell) {
        String str = "";
        if (cell == null) {
            return str;
        }
        if (cell.getCellType() == CellType.BLANK) { // 是否为空型
            str = "";
        } else if (cell.getCellType() == CellType.STRING) {// 是否为字符串型
            str = cell.getRichStringCellValue().toString();
        } else if (cell.getCellType() == CellType.NUMERIC) {// 是否为数值型
            if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {// 是否为日期型
                str = dateToString(cell.getDateCellValue(), "yyyy-MM-dd");
            } else {// 是否为数值型
//                cell.setCellType(CellType.STRING);
                return String.valueOf(cell.getNumericCellValue());
            }
        } else if (cell.getCellType() == CellType.FORMULA) {
            if (DateUtil.isCellDateFormatted(cell)) {// 是否为日期型
                str = dateToString(cell.getDateCellValue(), "yyyy-MM-dd");
            }
        } else if (cell.getCellType() == CellType.BOOLEAN) {// 是否为布尔型
            str = (cell.getBooleanCellValue() ? "1" : "0");
        }
        return str;
    }

    /**
     * 将日期型转变为String型的工具方法
     *
     * @param date    日期
     * @param pattern 日期格式
     * @return String的返回值
     */
    private static String dateToString(Date date, String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        String str = format.format(date);
        return str;
    }

    /**
     * @param url
     * @param sheetNO
     * @param beginrow
     * @return
     */
    public static String[][] getDatafromExcel(String url, int sheetNO, int beginrow) {
        String xlsPath = (Global.ProjectPath + url).replace("\\", "/").replace("//", "/");
        Sheet sheet = null;
        try {
            sheet = MyRule.getExcelSheets(xlsPath, sheetNO);
            int rowslen = sheet.getLastRowNum() + 1; // Use getLastRowNum() + 1 to get total number of rows
            int clen = sheet.getRow(0).getPhysicalNumberOfCells();
            String[][] arr = new String[rowslen - beginrow][clen];
            for (int r = beginrow; r < rowslen; r++) {
                Row row = sheet.getRow(r);
                if (row != null) {
                    String stemp = "";
                    for (int c = 0; c < clen; c++) {
                        Cell cell = row.getCell(c);
                        if (cell != null) {
                            stemp = getcellValue(cell);
                        } else {
                            stemp = "";
                        }
                        arr[r - beginrow][c] = stemp;
                    }
                }
            }
            return arr;
        } catch (Exception ex) {
            log.error("Exception", ex);
            return null;
        }
    }

    /**
     * 单元格样式
     *
     * @param wb
     * @return
     */
    private static HSSFCellStyle getCellStyle(HSSFWorkbook wb) {
        HSSFFont font = wb.createFont();
        font.setBold(true);
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setFont(font);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return style;
    }

    /**
     * 导出年体检报表
     *
     * @param param
     * @param request
     * @return
     */
    public static String exportReportExcel(JSONObject param, JSONObject param2, HttpServletRequest request) {
        log.info("exportExcelCheckResult");
        JSONObject objre = SendFySMS.checksms(param2);
        if (objre.has("error")) {
            return objre.toString();
        }
        String guid = (String) SecurityUtils.getAttribute("guid");
        if (!param.has("checkid")) {
            return objre.put("error", "请选择体检安排").toString();
        }
        //导出到的文件夹
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssSSSS");
        String strtm = format.format(new Date());
        Random r = new Random();
        int urlrandom = r.nextInt(1000);
        String outdir = "upload/check/report_" + strtm + urlrandom;
        File outDir = new File(Global.ProjectPath + outdir);
        if (!outDir.exists()) {
            outDir.mkdirs();
        }
        int checkid = param.optInt("checkid");
        String sql = "select cy.yeyname,cy.yeyid,cy.fyguid from check_child_yeyentry as cy left join yey on yey.id = cy.yeyid and yey.isdel = 0 where cy.isdel = 0 and cy.status = 2 and cy.check_id = " + checkid;
        if (param.has("yeyid")) {
            int yeyid = param.optInt("yeyid");
            sql += " and cy.yeyid = " + yeyid;
        }
        if (param.has("fyguid")) {
            String fyguid = param.optString("fyguid");
            sql += " and cy.fyguid = '" + fyguid + "'";
        }
        JSONArray arrError = new JSONArray();
        JSONArray arrYey = DBUtil.selectArray("f", guid, sql);
        for (int i = 0; i < arrYey.length(); i++) {
            JSONObject objYey = arrYey.optJSONObject(i);
            int yeyid = objYey.optInt("yeyid");
            String yeyname = objYey.optString("yeyname");
            String fyguid = objYey.optString("fyguid");
            param.put("yeyid", yeyid);
            param.put("yeyname", yeyname);
            param.put("fyguid", fyguid);
            try {
                exportReportByYey(param, outdir, request);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e);
                arrError.put(e.getMessage());
            }
        }
        //将文件夹压缩，并返回压缩文件路径
        ZIPUtil.compressManyToOne(Global.ProjectPath + outdir, Global.ProjectPath + outdir + ".zip");
        outdir = outdir.replace("\\", "/");
        if (arrError.length() > 0) {
            objre.put("error", arrError).toString();
        }
        return objre.put("path", outdir + ".zip").toString();
    }

    private static void exportReportByYey(JSONObject param, String outdir, HttpServletRequest request) throws Exception {
        log.info("exportReportByYey");
        String guid = (String) SecurityUtils.getAttribute("guid");
        int checkid = param.optInt("checkid");
        int yeyid = param.optInt("yeyid");
        String fyguid = param.optString("fyguid");
        String yeyname = param.optString("yeyname");
        String sql = "select ccr.id,ordernumber,credentialsnum,ccr.yeyid,ccr.yeyname,classno,claname,stuno,stuname,sex,to_char(birthday,'YYYY-MM-DD') as birthday,age,filenumber,location,tsort,hest,hight,weight,inheight,inweight,inhewe,agehe,agewe,hewe,comfat,fat,wufat,malnu,ahstandard,awstandard,whstandard,bmi,hw_doctor,remark,to_char(rday,'YYYY-MM-DD') as rday,hw_zhuanz_state,hw_zhuanz_yuanyin,hw_zhuanz_jgks,leye,reye,eyeage,lrank,rrank,islow,eye_zhuanz_state,eye_zhuanz_yuanyin,eye_zhuanz_jgks,eye_doctor,to_char(eyecheckdate,'YYYY-MM-DD') as eyecheckdate,carnum,car,iscar,car1,car2,car3,car3my,checked,car4,carcom1my,carcom2my,car_zhuanz_state,car_zhuanz_yuanyin,car_zhuanz_jgks,op_doctor,to_char(cardate,'YYYY-MM-DD') as cardate,ischeck,islisten,llis,rlis,tlscff,oae,lis_doctor,to_char(lis_ckdate,'YYYY-MM-DD') as lis_ckdate,lis_zhuanz_state,lis_zhuanz_yuanyin,lis_zhuanz_jgks,xiongbu,xiongbu_beizhu,xiongbu_content,abdomen_status,fubu_beizhu,fubu_content,fypg_state,fypg_content,fypg_age,huanb_state,huanb_content,xf_zhuanz_state,xf_zhuanz_yuanyin,xf_zhuanz_jgks,guidance,guidance_other,check_doctor,xf_doctor,to_char(xf_ckdate,'YYYY-MM-DD') as xf_ckdate,to_char(next_check_date,'YYYY-MM-DD') as next_check_date,altnum,gotnum,hematin,hematindivision,hematin_beizhu,hematin_zhuanz_state,hematin_zhuanz_yuanyin,hematin_zhuanz_jgks,hematin_doctor,to_char(hematin_ckdate,'YYYY-MM-DD') as hematin_ckdate,fhematin,tw,twpj,bust,liver,spleen,heart,lung,tonsil,islchl,isrchl,fcheckdate,fleye,fleyerank,freye,frrank,isflow,hleye,hleyerank,hreye,hreyerank,to_char(hcheckdate,'YYYY-MM-DD') as hcheckdate,eyecheck,ishlow,ishospital,leyeposition,reyeposition,lseye,lceye,rseye,rceye,to_char(eyedioptercheckdate,'yyyy-mm-dd') as eyedioptercheckdate,eyediopterage,lsrank,lcrank,rsrank,rcrank,eyediopterrank,yan,yan_wgr,yan_scjg,yan_l,yan_r,yan_lse,yan_lds,yan_ldc,yan_rse,yan_rds,yan_rdc,yan_content,yan_zhuanz_state,yan_zhuanz_yuanyin,yan_zhuanz_jgks,yan_doctor,to_char(yan_ckdate,'YYYY-MM-DD') as yan_ckdate,kouqiang,kouqiang_content,kq_zhuanz_state,kq_zhuanz_yuanyin,kq_zhuanz_jgks,kq_doctor,to_char(kq_ckdate,'YYYY-MM-DD') as kq_ckdate,ccr.status,ccr.llis_show,ccr.rlis_show,ccr.llis_show_beizhu,ccr.llis_beizhu from check_child_result ccr left join check_child_yeyentry ccy on ccy.check_id=ccr.check_id and ccy.yeyid=ccr.yeyid and ccr.fyguid = ccy.fyguid and ccy.isdel=0 where ccr.isdel=0 and ccr.check_id = " + checkid + " and ccr.yeyid = " + yeyid + " and ccr.fyguid = '" + fyguid + "'";
        if (param.has("organid")) {
            sql += "  and ccr.organ_id = " + param.optInt("organid");
        }
        if (param.has("banji")) {
            sql += "  and ccr.classno = '" + param.optString("banji") + "'";
        }
        if (param.has("packageid")) {
            sql += "  and position('," + param.optInt("packageid") + ",' in ',' || ccr.package_id || ',') > 0";
        }
        if (param.has("stuname")) {
            sql += "  and stuname like '%" + param.optString("stuname") + "%'";
        }
        if (param.has("sex")) {
            sql += "  and sex = '" + param.optString("sex") + "'";
        }
        if (param.has("status")) {
            sql += "  and ccr.status = '" + param.optString("status") + "'";
        }
        if (param.has("check_doctor")) {
            sql += "  and check_doctor like '%" + param.optString("check_doctor") + "%'";
        }
        if (param.has("publish")) {
            sql += "  and isclasfinish = " + param.optInt("publish");
        }
        if (param.has("overweight")) {
            sql += "  and (wufat is not null and wufat != '' and wufat != '正常')";
        }
        if (param.has("malnu")) {
            sql += "  and (malnu is not null and malnu != '' and malnu != '正常')";
        }
        if (param.has("hematindivision")) {
            sql += "  and (hematindivision is not null and hematindivision != '' and hematindivision != '正常')";
        }
        if (param.has("yan")) {
            sql += "  and ((islow = " + param.optJSONArray("yan").optInt(0) + " and ((leye is not null and leye != '') or (reye is not null and reye != ''))) or yan_scjg = " + param.optJSONArray("yan").optInt(1) + ")";
        }
        if (param.has("lis")) {
            sql += "  and (llis = " + param.optJSONArray("lis").optInt(0) + " or rlis = " + param.optJSONArray("lis").optInt(1) + " or yan_state = 3)";
        }
        if (param.has("kouqiang")) {
//            sql += "  and (car1 > 0 or kouqiang = " + param.optInt("kouqiang") + ")";
//            sql += "  and (iscar = '有')";
            sql += "  and (NULLIF(NULLIF(kouqiang_content, '')::json->'5'->>'n1'::text, '')::INTEGER > 0)";
        }
        if (param.has("xiongfu")) {
            sql += "  and (xiongbu = " + param.optJSONArray("xiongfu").optInt(0) + " or abdomen_status = " + param.optJSONArray("xiongfu").optInt(0) + ")";
        }
        if (param.has("fee_status")) {
            sql += "  and fee_status = " + param.optInt("fee_status");
        }
        if (param.has("fypg_state")) {
            sql += "  and fypg_state = " + param.optInt("fypg_state");
        }
        if (param.has("startdate")) {
            sql += "  and (to_char(rday,'yyyy-mm-dd') >= '" + param.optString("startdate") + "' or to_char(yan_ckdate,'yyyy-mm-dd') >= '" + param.optString("startdate") + "' or to_char(lis_ckdate,'yyyy-mm-dd') >= '" + param.optString("startdate") + "' or to_char(hematin_ckdate,'yyyy-mm-dd') >= '" + param.optString("startdate") + "' or to_char(kq_ckdate,'yyyy-mm-dd') >= '" + param.optString("startdate") + "' or to_char(xf_ckdate,'yyyy-mm-dd') >= '" + param.optString("startdate") + "')";
        }
        if (param.has("endtdate")) {
            sql += "  and (to_char(rday,'yyyy-mm-dd') <= '" + param.optString("endtdate") + "' or to_char(yan_ckdate,'yyyy-mm-dd') <= '" + param.optString("endtdate") + "' or to_char(lis_ckdate,'yyyy-mm-dd') <= '" + param.optString("endtdate") + "' or to_char(hematin_ckdate,'yyyy-mm-dd') <= '" + param.optString("endtdate") + "' or to_char(kq_ckdate,'yyyy-mm-dd') <= '" + param.optString("endtdate") + "' or to_char(xf_ckdate,'yyyy-mm-dd') <= '" + param.optString("endtdate") + "')";
        }

        if (param.has("complete")) {
            JSONArray arrcomplete = param.optJSONArray("complete");
            for (int i = 0; i < arrcomplete.length(); i++) {
                String key = arrcomplete.optJSONArray(i).optString(0);
                if(key.equals("rday")){
                    sql += "  and (rday is not null and hight is not null and weight is not null)";
                }
                if(key.equals("yan_ckdate")){
                    sql += "  and (yan_ckdate is not null and ((leye is not null and leye != '' and reye is not null and reye != '') or (yan_scjg is not null and yan_scjg != 3)))";
                }
                if(key.equals("lis_ckdate")){
                    sql += "  and (lis_ckdate is not null and llis is not null and rlis is not null)";
                }
                if(key.equals("hematin_ckdate")){
                    sql += "  and (hematin_ckdate is not null and hematin is not null)";
                }
                if(key.equals("kq_ckdate")){
                    sql += "  and (kq_ckdate is not null and carnum is not null)";
                }
                if(key.equals("xf_ckdate")){
                    sql += "  and (xf_ckdate is not null and xiongbu is not null and abdomen_status is not null)";
                }
            }
        }
        if (param.has("notcomplete")) {
            sql += " and (";
            JSONArray arrnotcomplete = param.optJSONArray("notcomplete");
            for (int i = 0; i < arrnotcomplete.length(); i++) {
                String key = arrnotcomplete.optJSONArray(i).optString(0);
                if(key.equals("rday")){
                    sql += "  or (rday is null or hight is null or weight is null)";
                }
                if(key.equals("yan_ckdate")){
                    sql += "  or (yan_ckdate is null or ((leye is null or leye = '' or reye is null or reye = '') and (yan_scjg is null or yan_scjg = 3)))";
                }
                if(key.equals("lis_ckdate")){
                    sql += "  or (lis_ckdate is null or llis is null or rlis is null)";
                }
                if(key.equals("hematin_ckdate")){
                    sql += "  or (hematin_ckdate is null or hematin is null)";
                }
                if(key.equals("kq_ckdate")){
                    sql += "  or (kq_ckdate is null or carnum is null)";
                }
                if(key.equals("xf_ckdate")){
                    sql += "  or (xf_ckdate is null or xiongbu is null or abdomen_status is null)";
                }
            }
            sql += " )";
        }
        sql += " order by ccr.classno,stuname asc";
        JSONArray arrResult = DBUtil.selectArray("f", guid, sql);
        String outpath = outdir + "/" + yeyname + ".xlsx";
        String relativePathOut = Global.ProjectPath + outpath;//导出路径
        String sourcePath = FuYouHandler.class.getClassLoader().getResource("templates").getPath();

        String relativePathIn = sourcePath + File.separator + "check" + File.separator + "tjnbb.xlsx";//空白模板
        FileOutputStream out = null;
        try (XSSFWorkbook wb = new XSSFWorkbook(new FileInputStream(relativePathIn))) {
            XSSFSheet sheet = wb.getSheetAt(0);
            for (int i = 0; i < arrResult.length(); i++) {
                JSONObject objResult = arrResult.optJSONObject(i);
                XSSFRow contentRow = sheet.createRow(i + 1);
                // 创建单元格并填充数据
                //ccr.id,ordernumber,credentialsnum,ccr.yeyid,ccr.yeyname,classno,claname,stuno,stuname,sex,to_char(birthday,'YYYY-MM-DD') as birthday,age,filenumber,location,tsort,hest,hight,weight,inheight,inweight,inhewe,agehe,agewe,hewe,comfat,fat,wufat,malnu,ahstandard,awstandard,whstandard,bmi,hw_doctor,remark,to_char(rday,'YYYY-MM-DD') as rday,hw_zhuanz_state,hw_zhuanz_yuanyin,hw_zhuanz_jgks,leye,reye,eyeage,lrank,rrank,islow,eye_zhuanz_state,eye_zhuanz_yuanyin,eye_zhuanz_jgks,eye_doctor,to_char(eyecheckdate,'YYYY-MM-DD') as eyecheckdate,carnum,car,iscar,car1,car2,car3,car3my,checked,car4,carcom1my,carcom2my,car_zhuanz_state,car_zhuanz_yuanyin,car_zhuanz_jgks,op_doctor,to_char(cardate,'YYYY-MM-DD') as cardate,ischeck,islisten,llis,rlis,tlscff,oae,lis_doctor,to_char(lis_ckdate,'YYYY-MM-DD') as lis_ckdate,lis_zhuanz_state,lis_zhuanz_yuanyin,lis_zhuanz_jgks,xiongbu,xiongbu_beizhu,xiongbu_content,abdomen_status,fubu_beizhu,fubu_content,fypg_state,fypg_content,fypg_age,huanb_state,huanb_content,xf_zhuanz_state,xf_zhuanz_yuanyin,xf_zhuanz_jgks,guidance,guidance_other,check_doctor,xf_doctor,to_char(xf_ckdate,'YYYY-MM-DD') as xf_ckdate,to_char(next_check_date,'YYYY-MM-DD') as next_check_date,altnum,gotnum,hematin,hematindivision,hematin_beizhu,hematin_zhuanz_state,hematin_zhuanz_yuanyin,hematin_zhuanz_jgks,hematin_doctor,to_char(hematin_ckdate,'YYYY-MM-DD') as hematin_ckdate,fhematin,tw,twpj,bust,liver,spleen,heart,lung,tonsil,islchl,isrchl,fcheckdate,fleye,fleyerank,freye,frrank,isflow,hleye,hleyerank,hreye,hreyerank,to_char(hcheckdate,'YYYY-MM-DD') as hcheckdate,eyecheck,ishlow,ishospital,leyeposition,reyeposition,lseye,lceye,rseye,rceye,to_char(eyedioptercheckdate,'yyyy-mm-dd') as eyedioptercheckdate,eyediopterage,lsrank,lcrank,rsrank,rcrank,eyediopterrank,yan,yan_l,yan_r,yan_lse,yan_lds,yan_ldc,yan_rse,yan_rds,yan_rdc,yan_content,yan_zhuanz_state,yan_zhuanz_yuanyin,yan_zhuanz_jgks,yan_doctor,to_char(yan_ckdate,'YYYY-MM-DD') as yan_ckdate,kouqiang,kouqiang_content,kq_zhuanz_state,kq_zhuanz_yuanyin,kq_zhuanz_jgks,kq_doctor,to_char(kq_ckdate,'YYYY-MM-DD') as kq_ckdate,ccr.status
                createCellAndSetVal(contentRow, 0, objResult.optString("claname"));//班级
                createCellAndSetVal(contentRow, 1, objResult.optString("stuname"));//姓名
                createCellAndSetVal(contentRow, 2, objResult.optString("sex"));//性别
                createCellAndSetVal(contentRow, 3, objResult.optString("birthday"));//出生日期
                createCellAndSetVal(contentRow, 4, getZhAgeByAge(objResult.optString("age")));//年龄
                createCellAndSetVal(contentRow, 5, objResult.optInt("hest") == 1 ? "卧位" : "立位");//卧立位
                createCellAndSetVal(contentRow, 6, objResult.optString("hight"));//身高
                //createCellAndSetVal(contentRow, 7, objResult.optString("inheight"));//身高增长
                createCellAndSetVal(contentRow, 7, objResult.optString("agehe"));//年龄别身高
                createCellAndSetVal(contentRow, 8, objResult.optString("weight"));//体重
                //createCellAndSetVal(contentRow, 10, objResult.optString("inweight"));//体重增长
                int inhewe = objResult.optInt("inhewe");
                //createCellAndSetVal(contentRow, 11, inhewe == -1 ? "" : inhewe == 0 ? "不合格" : "合格");//身高体重增长
                createCellAndSetVal(contentRow, 9, objResult.optString("agewe"));//年龄别体重
                createCellAndSetVal(contentRow, 10, objResult.optString("hewe"));//身高别体重
                String showpj = "";
                if(StringUtil.notNull(objResult.optString("wufat"))&& !objResult.optString("wufat").equals("正常")){
                    showpj = objResult.optString("wufat");
                }
                if(StringUtil.notNull(objResult.optString("malnu")) && !objResult.optString("malnu").equals("正常")){
                    String hewe = objResult.optString("hewe");
                    String agehe = objResult.optString("agehe");
                    String agewe = objResult.optString("agewe");
                    if(hewe.equals("下")){
                        showpj += "  消瘦";//营养不良
                    }
                    if(agehe.equals("下")){
                        showpj += "  生长迟缓";//营养不良
                    }
                    if(agewe.equals("下")){
                        showpj += "  低体重";//营养不良
                    }
                }
                if(StringUtil.isNull(showpj) && (objResult.optString("wufat").equals("正常") || objResult.optString("malnu").equals("正常"))){
                    showpj = "正常";
                }
                createCellAndSetVal(contentRow, 11, showpj);//体格发育评价
                createCellAndSetVal(contentRow, 12, objResult.optString("bmi"));//BMI值
                String hwZhuanZhen = "";
                int hw_zhuanz_state = objResult.optInt("hw_zhuanz_state");
                if (hw_zhuanz_state == 2) {//有
                    hwZhuanZhen = "有。\n" +
                            "原因：" + objResult.optString("hw_zhuanz_yuanyin") + "。\n" +
                            "机构及科室：" + objResult.optString("hw_zhuanz_jgks");
                } else {//无
                    hwZhuanZhen = "无";
                }
                createCellAndSetVal(contentRow, 13, hwZhuanZhen);//转诊建议
                createCellAndSetVal(contentRow, 14, objResult.optString("hw_doctor"));//体检医生
                createCellAndSetVal(contentRow, 15, objResult.optString("rday"));//检查日期
                String yan = objResult.optString("yan");
                createCellAndSetVal(contentRow, 16, "1".equals(yan) ? "正常" : "2".equals(yan) ? "异常" : "未检查");//左眼外观
                String yan_wgr = objResult.optString("yan_wgr");
                createCellAndSetVal(contentRow, 17, "1".equals(yan_wgr) ? "正常" : "2".equals(yan_wgr) ? "异常" : "未检查");//右眼外观
                createCellAndSetVal(contentRow, 18, objResult.optString("yan_content"));//备注
                createCellAndSetVal(contentRow, 19, objResult.optString("leye"));//左眼视力
                createCellAndSetVal(contentRow, 20, objResult.optString("lrank"));//左眼视力评价
                createCellAndSetVal(contentRow, 21, objResult.optString("reye"));//右眼视力
                createCellAndSetVal(contentRow, 22, objResult.optString("rrank"));//右眼视力评价
                String yan_scjg = objResult.optString("yan_scjg");
                createCellAndSetVal(contentRow, 23, "1".equals(yan_scjg) ? "未见异常" : "2".equals(yan_scjg) ? "异常" : "未检查");//筛查结果
                createCellAndSetVal(contentRow, 24, objResult.optString("yan_lse"));//左SE
                createCellAndSetVal(contentRow, 25, objResult.optString("yan_lds"));//左DS
                createCellAndSetVal(contentRow, 26, objResult.optString("yan_ldc"));//左DC
                createCellAndSetVal(contentRow, 27, objResult.optString("yan_rse"));//右SE
                createCellAndSetVal(contentRow, 28, objResult.optString("yan_rds"));//右DS
                createCellAndSetVal(contentRow, 29, objResult.optString("yan_rdc"));//右DC
                createCellAndSetVal(contentRow, 30, getZhuanzJianyi(objResult.optInt("yan_zhuanz_state"), objResult.optString("yan_zhuanz_yuanyin"), objResult.optString("yan_zhuanz_jgks")));//转诊建议
                createCellAndSetVal(contentRow, 31, objResult.optString("yan_doctor"));//体检医生
                createCellAndSetVal(contentRow, 32, objResult.optString("yan_ckdate"));//检查日期
                createCellAndSetVal(contentRow, 33, objResult.optString("llis_show","1").equals("1")?"未见异常":"异常");//左耳
                createCellAndSetVal(contentRow, 34, objResult.optString("rlis_show","1").equals("1")?"未见异常":"异常");//右耳
                createCellAndSetVal(contentRow, 35, objResult.optString("llis_show_beizhu"));//备注
                createCellAndSetVal(contentRow, 36, objResult.optInt("llis",1) == 1 ? "通过" :objResult.optInt("llis") == 2? "未通过":objResult.optInt("llis") == 3?"未检查":"其他");//左耳听力
                createCellAndSetVal(contentRow, 37, objResult.optInt("rlis",1) == 1 ? "通过" :objResult.optInt("rlis") == 2? "未通过":objResult.optInt("rlis") == 3?"未检查":"其他");//右耳听力
                createCellAndSetVal(contentRow, 38, objResult.optString("llis_beizhu"));//备注
                String tlscff = objResult.optString("tlscff");
                int oae = objResult.optInt("oae");
                String tlscffTemp = "";
                if ("1".equals(tlscff)) {
                    tlscffTemp = "听觉评估仪";
                } else {
                    if (oae == 1) {
                        tlscffTemp = "OAE耳声发射-瞬态";
                    } else {
                        tlscffTemp = "OAE耳声发射-畸变";
                    }
                }
                createCellAndSetVal(contentRow, 38, tlscffTemp);//筛查方法
                createCellAndSetVal(contentRow, 40, getZhuanzJianyi(objResult.optInt("lis_zhuanz_state"), objResult.optString("lis_zhuanz_yuanyin"), objResult.optString("lis_zhuanz_jgks")));//转诊建议
                createCellAndSetVal(contentRow, 41, objResult.optString("lis_doctor"));//体检医生
                createCellAndSetVal(contentRow, 42, objResult.optString("lis_ckdate"));//检查日期
                createCellAndSetVal(contentRow, 43, objResult.optString("hematin"));//血红蛋白（g/L）
                createCellAndSetVal(contentRow, 44, objResult.optString("hematindivision"));//评价
                createCellAndSetVal(contentRow, 45, objResult.optString("hematin_beizhu"));//备注
                createCellAndSetVal(contentRow, 46, getZhuanzJianyi(objResult.optInt("hematin_zhuanz_state"), objResult.optString("hematin_zhuanz_yuanyin"), objResult.optString("hematin_zhuanz_jgks")));//转诊建议
                createCellAndSetVal(contentRow, 47, objResult.optString("hematin_doctor"));//体检医生
                createCellAndSetVal(contentRow, 48, objResult.optString("hematin_ckdate"));//检查日期

                JSONObject objKqContent=null;
                String kouqiangStr;
                int kouqiang = objResult.optInt("kouqiang");
                if (kouqiang == 2) {//有
                    kouqiangStr = "有异常。\n";
                    String kouqiangContent = objResult.optString("kouqiang_content");
                    if (StringUtil.notNull(kouqiangContent)) {
                        objKqContent = new JSONObject(objResult.optString("kouqiang_content"));
                        String str = handleKqContent(objKqContent);
                        kouqiangStr += str;
                    }
                } else if(kouqiang==1){//无
                    kouqiangStr = "无异常";
                }else{
                    kouqiangStr = "未检查";
                }
                createCellAndSetVal(contentRow, 49, kouqiangStr);//口腔
                createCellAndSetVal(contentRow, 50, objResult.optString("carnum"));//牙齿总数
                String quchi = "";
                String qsbnum = "";
                if (objKqContent != null) {
                    JSONObject quObj = getQuchi(objKqContent);
                    quchi=quObj.optString("n1");
                    qsbnum=quObj.optString("n2");
                }
                createCellAndSetVal(contentRow, 51, quchi);//龋齿颗数
                createCellAndSetVal(contentRow, 52, qsbnum);//龋失补数
                createCellAndSetVal(contentRow, 53, getZhuanzJianyi(objResult.optInt("kq_zhuanz_state"),objResult.optString("kq_zhuanz_yuanyin"),objResult.optString("kq_zhuanz_jgks")));//转诊建议
                createCellAndSetVal(contentRow, 54, objResult.optString("kq_doctor"));//体检医生
                createCellAndSetVal(contentRow, 55, objResult.optString("kq_ckdate"));//检查日期

                //					胸部	备注	腹部	备注	发育评估	两次随访患病情况	其他检查	转诊建议	指导	下次随访日期	随访医生签名	体检医生	检查日期
                String xiongbuStr = "";
                int xiongbu = objResult.optInt("xiongbu");
                if (xiongbu == 1) {
                    xiongbuStr = "无异常";
                } else {
                    String xiongbuContent = objResult.optString("xiongbu_content");
                    String[] arrXbC = xiongbuContent.split(",");
                    List<String> XbCList = Arrays.asList(xiongbuContent.split(","));
                    String heartStr = "";
                    String hungStr = "";
                    for (int j = 0; j < arrXbC.length; j++) {
                        if ("2".equals(arrXbC[j])) {
                            heartStr += "心动过速，";
                        } else if ("3".equals(arrXbC[j])) {
                            heartStr += "心动过缓，";
                        } else if ("4".equals(arrXbC[j])) {
                            heartStr += "心脏杂音，";
                        } else if ("5".equals(arrXbC[j])) {
                            heartStr += "心率不齐，";
                        } else if ("6".equals(arrXbC[j])) {
                            hungStr += "呼吸音粗糙，";
                        } else if ("7".equals(arrXbC[j])) {
                            hungStr += "干啰音，";
                        } else if ("8".equals(arrXbC[j])) {
                            hungStr += "湿啰音，";
                        }
                    }
                    xiongbuStr = "有异常。\n" +
                            (XbCList.contains("1") ? "外观异常。\n" : "") +
                            (StringUtil.notNull(heartStr) ? "心脏听诊：" + heartStr.substring(0, heartStr.length() - 1) + "。\n" : "") +
                            (StringUtil.notNull(hungStr) ? "肺部听诊：" + hungStr.substring(0, hungStr.length() - 1) : "");
                }
                createCellAndSetVal(contentRow, 56, xiongbuStr);//胸部
                createCellAndSetVal(contentRow, 57, objResult.optString("xiongbu_beizhu"));//备注
                String fubuStr = "";
                int fubu = objResult.optInt("abdomen_status");
                if (fubu == 1) {
                    fubuStr = "无异常";
                } else {
                    String fubuContent = objResult.optString("fubu_content");
                    String[] arrFb = fubuContent.split(",");
                    for (int j = 0; j < arrFb.length; j++) {
                        if ("1".equals(arrFb[j])) {
                            fubuStr += "外观异常，";
                        } else if ("2".equals(arrFb[j])) {
                            fubuStr += "肝脏肿大，";
                        } else if ("3".equals(arrFb[j])) {
                            fubuStr += "脾脏肿大，";
                        } else if ("4".equals(arrFb[j])) {
                            fubuStr += "有压痛，";
                        } else if ("5".equals(arrFb[j])) {
                            fubuStr += "有包块，";
                        }
                    }
                    fubuStr = "有异常。\n" +
                            (StringUtil.notNull(fubuStr) ? fubuStr.substring(0, fubuStr.length() - 1) : "");
                }
                createCellAndSetVal(contentRow, 58, fubuStr);//腹部
                createCellAndSetVal(contentRow, 59, objResult.optString("fubu_beizhu"));//备注
                String fypgStr = "";
                int fypg = objResult.optInt("fypg_state");
                if (fypg == 2) {
                    String fypgContent = objResult.optString("fypg_content");
                    String[] arrFb = fypgContent.split(",");
                    for (int j = 0; j < arrFb.length; j++) {
                        if ("1".equals(arrFb[j])) {
                            fypgStr += "1.不会说自己的名字，";
                        } else if ("2".equals(arrFb[j])) {
                            fypgStr += "2.不会玩“拿棍当马骑”等假想游戏，";
                        } else if ("3".equals(arrFb[j])) {
                            fypgStr += "3.不会模仿画圆，";
                        } else if ("4".equals(arrFb[j])) {
                            fypgStr += "4.不会双脚跳，";
                        }
                    }
                    fypgStr = "有异常。\n" +
                            (StringUtil.notNull(fypgStr) ? fypgStr.substring(0, fypgStr.length() - 1) : "");
                } else {
                    fypgStr = "无异常";
                }
                createCellAndSetVal(contentRow, 60, fypgStr);//发育评估
                String huanbStr = "";
                int huanb = objResult.optInt("huanb_state");
                if (huanb == 2) {
                    String huanbContent = objResult.optString("huanb_content");
                    huanbStr = "有患病。\n";
                    if (StringUtil.notNull(huanbContent)) {
                        JSONObject objHuanbContent = new JSONObject(huanbContent);
                        huanbStr += "肺炎" + objHuanbContent.optInt("2") + "次。\n" +
                                "腹泻" + objHuanbContent.optInt("3") + "次。\n" +
                                "外伤" + objHuanbContent.optInt("4") + "次。\n" +
                                "其他：" + objHuanbContent.optString("5");
                    }
                } else {
                    huanbStr = "无患病";
                }
                createCellAndSetVal(contentRow, 61, huanbStr);//两次随访患病情况
                createCellAndSetVal(contentRow, 62, objResult.optString("remark"));//其他检查
                String xfZhuanZhen = "";
                int xf_zhuanz_state = objResult.optInt("xf_zhuanz_state");
                if (xf_zhuanz_state == 2) {//有
                    xfZhuanZhen = "有。\n" +
                            "原因：" + objResult.optString("xf_zhuanz_yuanyin") + "。\n" +
                            "机构及科室：" + objResult.optString("xf_zhuanz_jgks");
                } else {//无
                    xfZhuanZhen = "无";
                }
                createCellAndSetVal(contentRow, 63, xfZhuanZhen);//转诊建议
                String guidance = objResult.optString("guidance");
                String[] arrGuidance = guidance.split(",");
                String tempGuidance = "";
                for (int j = 0; j < arrGuidance.length; j++) {
                    if ("1".equals(arrGuidance[j])) {
                        tempGuidance += "合理膳食，";
                    } else if ("2".equals(arrGuidance[j])) {
                        tempGuidance += "生长发育，";
                    } else if ("3".equals(arrGuidance[j])) {
                        tempGuidance += "疾病预防，";
                    } else if ("4".equals(arrGuidance[j])) {
                        tempGuidance += "预防伤害，";
                    } else if ("5".equals(arrGuidance[j])) {
                        tempGuidance += "口腔保健，";
                    } else if ("6".equals(arrGuidance[j])) {
                        tempGuidance += "其他:" + objResult.optString("guidance_other") + "，";
                    }
                }
                createCellAndSetVal(contentRow, 64, StringUtil.notNull(tempGuidance) ? tempGuidance.substring(0, tempGuidance.length() - 1) : "");//指导
                createCellAndSetVal(contentRow, 65, objResult.optString("next_check_date"));//下次随访日期
                createCellAndSetVal(contentRow, 66, objResult.optString("check_doctor"));//随访医生签名
                createCellAndSetVal(contentRow, 67, objResult.optString("xf_doctor"));//体检医生
                createCellAndSetVal(contentRow, 68, objResult.optString("xf_ckdate"));//检查日期

            }
            out = new FileOutputStream(relativePathOut);
            wb.write(out);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e);
            throw new Exception(yeyname + "导出失败");
        } finally {
            close(out);
        }
    }

    private static void createCellAndSetVal(XSSFRow contentRow, int index, String text) {
        XSSFCell contentCell = contentRow.createCell(index);
        contentCell.setCellValue(text);
    }

    private static String handleKqContent(JSONObject objContent) {
        String str = "";
        if (objContent.has("2")) {
            str += "口腔卫生状况差，建议：加强口腔卫生、定期检查。\n";
        }
        if (objContent.has("3")) {
            str += "建议定期来院涂氟防龋。\n";
        }
        if (objContent.has("4")) {
            JSONObject objresult = objContent.optJSONObject("4");
            JSONArray arrjy = objresult.optJSONArray("jy");
            String strjy = "";
            if (arrjy.length() > 0) {
                strjy = "，建议：" + arrjy.join("，");
            }
            str += "牙面色素沉着" + strjy + "。\n";
        }
        if (objContent.has("5")) {
            JSONObject objresult = objContent.optJSONObject("5");
            int n1 = objresult.optInt("n1", 0);
            int n2 = objresult.optInt("n2", 0);
            int n3 = objresult.optInt("n3", 0);
            int n4 = objresult.optInt("n4", 0);
            JSONArray arrjy = objresult.optJSONArray("jy");
            String strjy = "";
            if (arrjy.length() > 0) {
                strjy = "，建议：" + arrjy.join("，");
            }
            str += "龋齿" + n1 + "颗龋坏，龋失补牙数" + n2 + "颗，已填充无龋数" + n3 + "颗，因龋缺失数" + n4 + "颗" + strjy + "。\n";
        }
        if (objContent.has("6") || objContent.has("7") || objContent.has("14")) {
            JSONArray arrstr = new JSONArray();
            if (objContent.has("6")) {
                arrstr.put("深窝沟");
            }
            if (objContent.has("7")) {
                arrstr.put("六龄牙");
            }
            if (objContent.has("14")) {
                arrstr.put("乳磨牙");
            }
//            JSONObject objresult = objContent.has("6") ? objContent.optJSONObject("6") : objContent.has("7") ? objContent.optJSONObject("7") : new JSONObject().put("jy", "[窝沟封闭]");
//            JSONArray arrjy = objresult.optJSONArray("jy");
            String strjy = "，建议：窝沟封闭";
//            if(arrjy.length() > 0){
//                strjy = "，建议：" + arrjy.join("，");
//            }
            str += arrstr.join("，") + strjy + "。\n";
        }
        if (objContent.has("8") || objContent.has("9")) {
            JSONArray arrstr = new JSONArray();
            if (objContent.has("8")) {
                arrstr.put("后牙反颌");
            }
            if (objContent.has("9")) {
                arrstr.put("前牙反颌");
            }
            if (objContent.has("15")) {
                arrstr.put("前牙开颌");
            }
            if (objContent.has("16")) {
                arrstr.put("深覆颌");
            }
            if (objContent.has("17")) {
                arrstr.put("锁颌");
            }
//            JSONObject objresult = objContent.has("8") ? objContent.optJSONObject("8") : objContent.has("9") ? objContent.optJSONObject("9") :  new JSONObject().put("jy", "[择期治疗]");
//            JSONArray arrjy = objresult.optJSONArray("jy");
            String strjy = "，建议：择期治疗";
//            if(arrjy.length() > 0){
//                strjy = "，建议：" + arrjy.join("，");
//            }
            str += arrstr.join("，") + strjy + "。\n";
        }
        if (objContent.has("10")) {
            JSONObject objresult = objContent.optJSONObject("10");
            int n1 = objresult.optInt("n1", 0);
            int n2 = objresult.optInt("n2", 0);
            str += "乳牙滞留" + n1 + "已萌出，" + n2 + "滞留，建议：拔除滞留乳牙。\n";
        }
        if (objContent.has("11")) {
            JSONObject objresult = objContent.optJSONObject("11");
            int n1 = objresult.optInt("n1", 0);
            str += "乳牙早失" + n1 + "过早脱落，建议：间隙维持。\n";
        }
        if (objContent.has("12")) {
            JSONObject objresult = objContent.optJSONObject("12");
            JSONArray arrjy = objresult.optJSONArray("jy");
            String strjy = "";
            if (arrjy.length() > 0) {
                strjy = "，建议：" + arrjy.join("，");
            }
            str += "舌系带过短" + strjy + "。\n";
        }
        if (objContent.has("13")) {
            JSONObject objresult = objContent.optJSONObject("13");
            JSONArray arrjy = objresult.optJSONArray("jy");
            String strjy = "";
            if (arrjy.length() > 0) {
                strjy = "，建议：" + arrjy.join("，");
            }
            str += "上唇系带过低" + strjy + "。\n";
        }
        if (objContent.has("18")) {
            JSONObject objresult = objContent.optJSONObject("18");
            str += "融合牙。\n";
        }
        if (objContent.has("1")) {
            JSONObject objresult = objContent.optJSONObject("1");
            str += "其他：" + objresult.optString("other");
        }
        return str;
    }

    private static JSONObject getQuchi(JSONObject objContent) {
        JSONObject objresult = new JSONObject();
        if (objContent.has("5")) {
            objresult = objContent.optJSONObject("5");
        }
        return objresult;
    }


    public static String exportchildlist(String where, JSONObject param, HttpServletRequest request) {
        log.info("exportinchecklist");
        JSONObject objre = SendFySMS.checksms(param);
        if (objre.has("error")) {
            return objre.toString();
        }
        String guid = (String) SecurityUtils.getAttribute("guid");
        String sql = "select s.yeyid,yeytype,yeyname,stuno,stuname,sex,to_char(birthday,'yyyy-MM-dd') as birthday,nation,localtion,locationtype,filenumber,birthnumber,to_char(intime,'yyyy-MM-dd') as intime,s.status from ek_student s left join yey on s.yeyid=yey.id and (relationstatus='yes' or ptid is null) where s.isdel=0" + where;
        sql += " order by yeyid,stuname";
        JSONArray arrYey = DBUtil.selectArray("f", guid, sql);
        String filepath = "";
        HSSFWorkbook wb = null;
        FileOutputStream fout = null;
        try {
            // 第一步，创建一个webbook，对应一个Excel文件
            wb = new HSSFWorkbook();
            HSSFSheet sheet = null;
            // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
            sheet = wb.createSheet();
//        sheet.setColumnWidth(0, 25000);
            HSSFCellStyle cellStyle = getCellStyle(wb);
            // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
            HSSFRow row = sheet.createRow(0);
            // 第四步，创建单元格，并设置值表头 设置表头居中
            HSSFCell cell = row.createCell(0);
            cell.setCellValue("托幼机构类型");
            cell = row.createCell(1);
            cell.setCellValue("托幼机构名称");
            cell = row.createCell(2);
            cell.setCellValue("姓名");
            cell = row.createCell(3);
            cell.setCellValue("性别");
            cell = row.createCell(4);
            cell.setCellValue("出生日期");
            cell = row.createCell(5);
            cell.setCellValue("年龄");
            cell = row.createCell(6);
            cell.setCellValue("民族");
            cell = row.createCell(7);
            cell.setCellValue("户口所在地");
            cell = row.createCell(8);
            cell.setCellValue("档案号");
            cell = row.createCell(9);
            cell.setCellValue("出生证明号");
            cell = row.createCell(10);
            cell.setCellValue("入园日期");
            Date date = new Date();
            DateFormat formatdate = new SimpleDateFormat("yyyy-MM-dd");
            for (int i = 0; i < arrYey.length(); i++) {
                JSONObject objdata = arrYey.optJSONObject(i);
                HSSFRow contentRow = sheet.createRow(i + 1);
                // 第五步，创建单元格并填充数据
                HSSFCell contentCell;
                contentCell = contentRow.createCell(0);
                int yeytype = objdata.optInt("yeytype");
                contentCell.setCellValue(yeytype == 1 ? "幼儿园" : yeytype == 3 ? "托育机构" : yeytype == 6 ? "托幼一体园" : "");
                contentCell = contentRow.createCell(1);
                contentCell.setCellValue(objdata.optString("yeyname"));
                contentCell = contentRow.createCell(2);
                contentCell.setCellValue(objdata.optString("stuname"));
                contentCell = contentRow.createCell(3);
                contentCell.setCellValue(objdata.optString("sex"));
                contentCell = contentRow.createCell(4);
                contentCell.setCellValue(objdata.optString("birthday"));
                contentCell = contentRow.createCell(5);
                contentCell.setCellValue(CheckPublic.GetAgeFromDate(objdata.optString("birthday"), formatdate.format(date), "zh"));
                contentCell = contentRow.createCell(6);
                contentCell.setCellValue(objdata.optString("nation"));
                contentCell = contentRow.createCell(7);
                contentCell.setCellValue(objdata.optString("locationtype"));
                contentCell = contentRow.createCell(8);
                contentCell.setCellValue(objdata.optString("filenumber"));
                contentCell = contentRow.createCell(9);
                contentCell.setCellValue(objdata.optString("birthnumber"));
                contentCell = contentRow.createCell(10);
                contentCell.setCellValue(objdata.optString("intime"));
            }
            DateFormat format1 = new SimpleDateFormat("yyyyMMddHHmmss");
            Random r = new Random();
            int urlrandom = r.nextInt(1000);
            String eurl = format1.format(date) + urlrandom + ".xls";
            filepath = Global.ProjectPath + "upload/" + eurl;
            fout = new FileOutputStream(filepath);
            wb.write(fout);
            close(fout);
            wb.close();
            objre.put("name", eurl);
            objre.put("path", "upload/" + eurl);
        } catch (Exception e) {
            objre.put("error", "生成excel出错");
            log.error("生成excel出错", e);
        } finally {
            FileUtil.close(fout, wb);
        }
        return objre.toString();
    }

    public static String exportmdicallist(String where, JSONObject param, HttpServletRequest request) {
        log.info("exportmdicallist");
        JSONObject objre = SendFySMS.checksms(param);
        if (objre.has("error")) {
            return objre.toString();
        }
        JSONObject objwhere = new JSONObject(where);
        String guid = (String) SecurityUtils.getAttribute("guid");
        String sql = "select m.id,m.yeyid,m.yeyname, term, to_char(rday, 'yyyy-mm-dd') as rday, m.claname, m.classno, m.stuno, m.stuname,m.sex,to_char(m.birthday,'YYYY-MM-DD') as birthday, m.credentialsnum, age, m.filenumber, m.location,m.hest,m.tsort, m.hight,m.weight,comfat,leye,reye,lrank,rrank,islow,agehe,agewe,hewe,fat,malnu,wufat,bmi,ahstandard,awstandard,whstandard,check_doctor from tuoyu_mdicalrecord as m left join ek_student as es on m.yeyid=es.yeyid and m.stuno=es.stuno where  m.isdel=0";
        if (objwhere.has("mainid")) {
            sql += "  and mainid=" + objwhere.optInt("mainid");
        }
        if (objwhere.has("classno")) {
            sql += "  and m.classno='" + objwhere.optString("classno") + "'";
        }
        if (objwhere.has("stuname")) {
            sql += "  and m.stuname like '%" + objwhere.optString("stuname") + "%'";
        }
        sql += StringUtil.notNull(objwhere.optString("order")) ? " order by " + objwhere.optString("order") : " order by es.sex desc,es.birthday";
//        sql += " order by yeyid,stuname";
        JSONArray arrYey = DBUtil.selectArray("f", guid, sql);
        String filepath = "";
        HSSFWorkbook wb = null;
        FileOutputStream fout = null;
        try {
            // 第一步，创建一个webbook，对应一个Excel文件
            wb = new HSSFWorkbook();
            HSSFSheet sheet = null;
            // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
            sheet = wb.createSheet();
//        sheet.setColumnWidth(0, 25000);
            HSSFCellStyle cellStyle = getCellStyle(wb);
            // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
            HSSFRow row = sheet.createRow(0);
            // 第四步，创建单元格，并设置值表头 设置表头居中
            HSSFCell cell = row.createCell(0);
            cell.setCellValue("幼儿姓名");
            cell = row.createCell(1);
            cell.setCellValue("性别");
            cell = row.createCell(2);
            cell.setCellValue("年龄");
            cell = row.createCell(3);
            cell.setCellValue("出生日期");
            cell = row.createCell(4);
            cell.setCellValue("卧立位");
            cell = row.createCell(5);
            cell.setCellValue("身高/厘米");
            cell = row.createCell(6);
            cell.setCellValue("体重/公斤");
            cell = row.createCell(7);
            cell.setCellValue("评价");
            cell = row.createCell(8);
            cell.setCellValue("左眼");
            cell = row.createCell(9);
            cell.setCellValue("右眼");
            cell = row.createCell(10);
            cell.setCellValue("体检医生");
            cell = row.createCell(11);
            cell.setCellValue("检查日期");
            Date date = new Date();
            DateFormat formatdate = new SimpleDateFormat("yyyy-MM-dd");
            for (int i = 0; i < arrYey.length(); i++) {
                JSONObject objdata = arrYey.optJSONObject(i);
                HSSFRow contentRow = sheet.createRow(i + 1);
                // 第五步，创建单元格并填充数据
                HSSFCell contentCell;
                contentCell = contentRow.createCell(0);
                String stuname = objdata.optString("stuname");
                contentCell.setCellValue(stuname);
                contentCell = contentRow.createCell(1);
                contentCell.setCellValue(objdata.optString("sex"));
                contentCell = contentRow.createCell(2);
                contentCell.setCellValue(objdata.optString("age"));
                contentCell = contentRow.createCell(3);
                contentCell.setCellValue(objdata.optString("birthday"));
                contentCell = contentRow.createCell(4);
                contentCell.setCellValue(objdata.optInt("hest") == 1 ? "卧位" : "立位");
                contentCell = contentRow.createCell(5);
                contentCell.setCellValue(objdata.optString("hight"));
                contentCell = contentRow.createCell(6);
                contentCell.setCellValue(objdata.optString("weight"));
                contentCell = contentRow.createCell(7);
                String pjStr = "";
                if (!"正常".equals(objdata.optString("wufat"))) {
                    pjStr = objdata.optString("wufat");//五分法
                }
                if (!"正常".equals(objdata.optString("malnu"))) {
                    String malnu = CheckPublic.GetManupj(objdata.optString("hewe"), objdata.optString("agehe"), objdata.optString("agewe"));
                    pjStr += malnu;
                    // pjStr += "  " + d.malnu;//营养不良
                }
                if ("正常".equals(objdata.optString("wufat")) && "正常".equals(objdata.optString("malnu"))) {
                    pjStr = "正常";
                }
                contentCell.setCellValue(pjStr);
                contentCell = contentRow.createCell(8);
                contentCell.setCellValue(objdata.optString("leye"));
                contentCell = contentRow.createCell(9);
                contentCell.setCellValue(objdata.optString("reye"));
                contentCell = contentRow.createCell(10);
                contentCell.setCellValue(objdata.optString("check_doctor"));
                contentCell = contentRow.createCell(11);
                contentCell.setCellValue(objdata.optString("rday"));
            }
            DateFormat format1 = new SimpleDateFormat("yyyyMMddHHmmss");
            Random r = new Random();
            int urlrandom = r.nextInt(1000);
            String eurl = format1.format(date) + urlrandom + ".xls";
            filepath = Global.ProjectPath + "upload/" + eurl;
            fout = new FileOutputStream(filepath);
            wb.write(fout);
            close(fout);
            wb.close();
            objre.put("name", eurl);
            objre.put("path", "upload/" + eurl);
        } catch (Exception e) {
            objre.put("error", "生成excel出错");
            log.error("生成excel出错", e);
        } finally {
            FileUtil.close(fout, wb);
        }
        return objre.toString();
    }

    private static String getZhuanzJianyi(int zhuanz_state,String zhuanz_yuanyin,String zhuanz_jgks){
        String ZhuanZhen;
        if (zhuanz_state == 2) {//有
            ZhuanZhen = "有。\n" +
                    "原因：" + zhuanz_yuanyin + "。\n" +
                    "机构及科室：" + zhuanz_jgks;
        } else {//无
            ZhuanZhen = "无";
        }

        return ZhuanZhen;
    }

    private static void createCell(HSSFSheet sheet, HSSFRow row, CellStyle centerStyle, int firstRow, int lastRow, int firstCol, int lastCol, String cellVal){
        HSSFCell cell = row.createCell(firstCol);
        cell.setCellStyle(centerStyle);
        if(lastRow > firstRow || lastCol > firstCol){
            sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
        }
        cell.setCellValue(cellVal);
    }

    public static String exportrechecklist(String where, String recordneck, JSONObject param, HttpServletRequest request) {
        log.info("exportchecklist");
        JSONObject objre = SendFySMS.checksms(param);
        if (objre.has("error")) {
            return objre.toString();
        }

        JSONArray array = DBUtil.selectArray("fyyypt", "", "select sysno,sysvalue,sysway,sysremark,areacode from ek_system where isdel=0 and sysno in ('eyesort', 'norcar')");
        int eyesort = 1;
        int car = 0;
        for (int i = 0; i < array.length(); i++) {
            JSONObject objSet = array.optJSONObject(i);
            if("eyesort".equals(objSet.optString("sysno"))){
                eyesort = objSet.optInt("sysvalue");
            } else if("norcar".equals(objSet.optString("sysno"))){
                car = objSet.optInt("norcar");
            }
        }
        String guid = (String) SecurityUtils.getAttribute("guid");
        String sql = "select id,yeyid,yeyname,classno,claname,stuno,stuname,credentialsnum,sex,age,hight,weight,eyeage,leye,reye,lrank,rrank,islow,to_char(eyecheckdate,'YYYY-MM-DD') as eyecheckdate,fleye,fleyerank,freye,frrank,isflow,to_char(fcheckdate,'YYYY-MM-DD') as fcheckdate,hname,hleye,hleyerank,hreye,hreyerank,ishospital,eyecheck,ishlow,eyecorrect,eyecorrectway,eyecorrectotherway,hremark,to_char(hcheckdate,'YYYY-MM-DD') as hcheckdate,checked,car,car1,car2,car3,car4,car3my,to_char(cardate,'YYYY-MM-DD') as cardate,op_doctor,fchecked,fcar,fcar1,fcar2,fcar3,fcar4,fcar3my,to_char(fcardate,'YYYY-MM-DD') as fcardate,fop_doctor,hematin,hest,remark,to_char(rday,'YYYY-MM-DD') as rday,to_char(birthday,'YYYY-MM-DD') as birthday,filenumber,location,tsort,inheight,inweight,inhewe,agehe,agewe,hewe,comfat,fat,malnu,ahstandard,awstandard,whstandard,hematindivision,bmi,carcom1my,carcom2my,wufat,tw,twpj,bust,iscar,liver,spleen,heart,lung,tonsil,islchl,isrchl,ischeck,llis,rlis,fhematin,leyeposition,reyeposition,altnum,gotnum,lseye,lceye,rseye,rceye,to_char(eyedioptercheckdate,'yyyy-mm-dd') as eyedioptercheckdate,eyediopterage,lsrank,lcrank,rsrank,rcrank,eyediopterrank,ordernumber,status,check_doctor,check_id,organ_id,isrecheck,remind_num,submit_num,open_num,feye_photos,car_photos,flis_photos,fxf_photos,carnum,yan_scjg,yan_lse,yan_lds,yan_ldc,yan_rse,yan_rds,yan_rdc,kouqiang,kouqiang_content,xiongbu,abdomen_status,to_char(remind_time,'yyyy-mm-dd') as remind_time,check_status,abnormal_cause,abnormal_text,abnormal_other,to_char(abnormal_time,'yyyy-mm-dd') as abnormal_time from check_child_result where isdel=0 " + where;
        sql += " order by id";
        JSONArray arrResult = DBUtil.selectArray("f", guid, sql);
        String filepath = "";
        String areaname = param.optString("areaname");
        FileOutputStream fout = null;
        HSSFWorkbook wb = null;
        JSONArray arrColumnKey = new JSONArray();//缓存列对应的数据值所匹配的key
        try {
            // 第一步，创建一个webbook，对应一个Excel文件
            wb = new HSSFWorkbook();
            CellStyle centerStyle = createcenterStyle(wb);
            HSSFSheet sheet = null;
            // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
            sheet = wb.createSheet();
            //sheet.setColumnWidth(0, 25000);
            HSSFCellStyle cellStyle = getCellStyle(wb);
            // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
            HSSFRow row = sheet.createRow(0);
            HSSFRow row2 = sheet.createRow(1);
            int lastRow = 0;
            if(recordneck.contains("eye") || recordneck.contains("car") || recordneck.contains("listen")){
                lastRow = 1;
            }
            int curColumn = 0;
            // 第四步，创建单元格，并设置值表头 设置表头居中
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "序号");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "姓名");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "性别");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "年龄");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "班级");
            curColumn++;
            if(recordneck.contains("eye")){
                createCell(sheet, row, centerStyle, 0, 0, curColumn, curColumn + 2, "视力");
                if(eyesort == 1){//1表示先填左眼，后填右眼,2表示先填右眼，后填左眼
                    createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "左");
                    curColumn++;
                    createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "右");
                    curColumn++;
                } else {
                    createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "右");
                    curColumn++;
                    createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "左");
                    curColumn++;
                }
                createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "检查日期");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "屈光筛查结果");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "左SE");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "左DS");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "左DC");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "右SE");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "右DS");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "右DC");
                curColumn++;
            }

            if(recordneck.contains("car")){
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "口腔");
                curColumn++;
                if(car == 1){
                    createCell(sheet, row, centerStyle, 0, 0, curColumn, curColumn + 2, "龋齿");
                } else {
                    createCell(sheet, row, centerStyle, 0, 0, curColumn, curColumn + 3, "龋齿");
                    createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "有无");
                    curColumn++;
                }
                createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "龋失补数");
                curColumn++;
                createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "龋齿颗数");
                curColumn++;
                createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "牙齿总数");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "体检日期");
                curColumn++;
                createCell(sheet, row, centerStyle, 0, 1, curColumn, curColumn, "体检医生");
                curColumn++;
            }
            if(recordneck.contains("listen")){
                createCell(sheet, row, centerStyle, 0, 0, curColumn, curColumn + 1, "听力");
                createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "左耳");
                curColumn++;
                createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "右耳");
                curColumn++;
            }
            if(recordneck.contains("xiongbu")){
                createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "胸部");
                curColumn++;
            }
            if(recordneck.contains("abdomen_status")){
                createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "腹部");
                curColumn++;
            }
            createCell(sheet, row, centerStyle, 0, 0, curColumn, curColumn + 1, "复查结果");
            createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "复查结果");
            curColumn++;
            createCell(sheet, row2, centerStyle, 1, 1, curColumn, curColumn, "确诊日期");
            for (int i = 0; i < arrResult.length(); i++) {
                curColumn = 0;
                JSONObject objdata = arrResult.optJSONObject(i);
                HSSFRow contentRow = sheet.createRow(i + (lastRow + 1));
                // 第五步，创建单元格并填充数据
                HSSFCell contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(i + 1);
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("stuname"));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("sex"));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(getZhAgeByAge(objdata.optString("age")));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("claname"));
                curColumn++;
                if(recordneck.contains("eye")){
                    if(eyesort == 1) {//1表示先填左眼，后填右眼,2表示先填右眼，后填左眼
                        contentCell = contentRow.createCell(curColumn);
                        contentCell.setCellValue(objdata.optString("leye"));
                        curColumn++;
                        contentCell = contentRow.createCell(curColumn);
                        contentCell.setCellValue(objdata.optString("reye"));
                        curColumn++;
                    } else {
                        contentCell = contentRow.createCell(curColumn);
                        contentCell.setCellValue(objdata.optString("reye"));
                        curColumn++;
                        contentCell = contentRow.createCell(curColumn);
                        contentCell.setCellValue(objdata.optString("leye"));
                        curColumn++;
                    }
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("eyecheckdate"));
                    curColumn++;

                    int yan_scjg = objdata.optInt("yan_scjg");
                    String zztxt = yan_scjg == 1 ? "无异常" : yan_scjg == 2 ? "有异常" : "未检查";
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(zztxt);
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("yan_lse"));
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("yan_lds"));
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("yan_ldc"));
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("yan_rse"));
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("yan_rds"));
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("yan_rdc"));
                    curColumn++;
                }
                if(recordneck.contains("car")){
                    int kouqiang = objdata.optInt("kouqiang");
                    String zztxt = kouqiang == 1 ? "无异常" : kouqiang == 2 ? "有异常" : "未检查";
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(zztxt);
                    curColumn++;
                    String kouqiang_content = objdata.optString("kouqiang_content");
                    JSONObject objKQ = StringUtil.notNull(kouqiang_content) ? new JSONObject(kouqiang_content) : new JSONObject();
                    String car1 = objKQ.has("5") ? objKQ.optJSONObject("5").optString("n2") : "";
                    String quchiNum = objKQ.has("5") ? objKQ.optJSONObject("5").optString("n1") : "";
                    if(car == 1){
                    } else {
                        contentCell = contentRow.createCell(curColumn);
                        contentCell.setCellValue(objdata.optString("iscar"));
                        curColumn++;
                    }
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(car1);
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(quchiNum);
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("carnum"));
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("cardate"));
                    curColumn++;
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(objdata.optString("op_doctor"));
                    curColumn++;
                }

                if(recordneck.contains("listen")){
                    int llis = objdata.optInt("llis");
                    String zztxt = llis == 1 ? "通过" : llis == 2 ? "未通过" : llis == 3 ? "未检查" : "其他";
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(zztxt);
                    curColumn++;
                    int rlis = objdata.optInt("rlis");
                    String zztxtr = rlis == 1 ? "通过" : rlis == 2 ? "未通过" : rlis == 3 ? "未检查" : "其他";
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(zztxtr);
                    curColumn++;
                }
                if(recordneck.contains("xiongbu")){
                    int xiongbu = objdata.optInt("xiongbu");
                    String zztxt = xiongbu == 1 ? "无异常" : xiongbu == 2 ? "有异常" : "未检查";
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(zztxt);
                    curColumn++;
                }
                if(recordneck.contains("abdomen_status")){
                    int abdomen_status = objdata.optInt("abdomen_status");
                    String zztxt = abdomen_status == 1 ? "无异常" : abdomen_status == 2 ? "有异常" : "未检查";
                    contentCell = contentRow.createCell(curColumn);
                    contentCell.setCellValue(zztxt);
                    curColumn++;
                }
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("abnormal_text"));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("abnormal_time"));
            }
            DateFormat format1 = new SimpleDateFormat("yyyyMMddHHmmss");
            Random r = new Random();
            int urlrandom = r.nextInt(1000);
            Date date = new Date();
            String eurl = format1.format(date) + urlrandom + ".xls";
            filepath = Global.ProjectPath + "upload/" + eurl;
            fout = new FileOutputStream(filepath);
            wb.write(fout);
            close(fout);
            wb.close();
            objre.put("name", eurl);
            objre.put("path", "upload/" + eurl);
        } catch (Exception e) {
            objre.put("error", "生成excel出错");
            log.error("生成excel出错", e);
        } finally {
            FileUtil.close(fout, wb);
        }
        return objre.toString();
    }
    public static CellStyle createcenterStyle(Workbook wb) {
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 指定单元格居中对齐HSSFCellStyle.ALIGN_CENTER
        style.setVerticalAlignment(VerticalAlignment.CENTER);// 指定单元格垂直居中对齐HSSFCellStyle.VERTICAL_CENTER
        return style;
    }

    public static String exportrechecksimplelist(String where, String recordneck, JSONObject param, HttpServletRequest request) {
        log.info("exportrechecksimplelist");
        JSONObject objre = SendFySMS.checksms(param);
        if (objre.has("error")) {
            return objre.toString();
        }

        JSONArray array = DBUtil.selectArray("fyyypt", "", "select sysno,sysvalue,sysway,sysremark,areacode from ek_system where isdel=0 and sysno in ('eyesort', 'norcar')");
        int eyesort = 1;
        int car = 0;
        for (int i = 0; i < array.length(); i++) {
            JSONObject objSet = array.optJSONObject(i);
            if("eyesort".equals(objSet.optString("sysno"))){
                eyesort = objSet.optInt("sysvalue");
            } else if("norcar".equals(objSet.optString("sysno"))){
                car = objSet.optInt("norcar");
            }
        }
        String guid = (String) SecurityUtils.getAttribute("guid");
        String sql = "select id,yeyid,yeyname,classno,claname,stuno,stuname,credentialsnum,sex,age,hight,weight,eyeage,leye,reye,lrank,rrank,islow,to_char(eyecheckdate,'YYYY-MM-DD') as eyecheckdate,fleye,fleyerank,freye,frrank,isflow,to_char(fcheckdate,'YYYY-MM-DD') as fcheckdate,hname,hleye,hleyerank,hreye,hreyerank,ishospital,eyecheck,ishlow,eyecorrect,eyecorrectway,eyecorrectotherway,hremark,to_char(hcheckdate,'YYYY-MM-DD') as hcheckdate,checked,car,car1,car2,car3,car4,car3my,to_char(cardate,'YYYY-MM-DD') as cardate,op_doctor,fchecked,fcar,fcar1,fcar2,fcar3,fcar4,fcar3my,to_char(fcardate,'YYYY-MM-DD') as fcardate,fop_doctor,hematin,hest,remark,to_char(rday,'YYYY-MM-DD') as rday,to_char(birthday,'YYYY-MM-DD') as birthday,filenumber,location,tsort,inheight,inweight,inhewe,agehe,agewe,hewe,comfat,fat,malnu,ahstandard,awstandard,whstandard,hematindivision,bmi,carcom1my,carcom2my,wufat,tw,twpj,bust,iscar,liver,spleen,heart,lung,tonsil,islchl,isrchl,ischeck,llis,rlis,fhematin,leyeposition,reyeposition,altnum,gotnum,lseye,lceye,rseye,rceye,to_char(eyedioptercheckdate,'yyyy-mm-dd') as eyedioptercheckdate,eyediopterage,lsrank,lcrank,rsrank,rcrank,eyediopterrank,ordernumber,status,check_doctor,check_id,organ_id,isrecheck,remind_num,submit_num,open_num,feye_photos,car_photos,flis_photos,fxf_photos,carnum,yan_scjg,yan_lse,yan_lds,yan_ldc,yan_rse,yan_rds,yan_rdc,kouqiang,kouqiang_content,xiongbu,abdomen_status,to_char(remind_time,'yyyy-mm-dd') as remind_time,check_status,abnormal_cause,abnormal_text,abnormal_other,to_char(abnormal_time,'yyyy-mm-dd') as abnormal_time from check_child_result where isdel=0 " + where;
        sql += " order by id";
        JSONArray arrResult = DBUtil.selectArray("f", guid, sql);
        String filepath = "";
        String areaname = param.optString("areaname");
        FileOutputStream fout = null;
        HSSFWorkbook wb = null;
        JSONArray arrColumnKey = new JSONArray();//缓存列对应的数据值所匹配的key
        try {
            // 第一步，创建一个webbook，对应一个Excel文件
            wb = new HSSFWorkbook();
            CellStyle centerStyle = createcenterStyle(wb);
            HSSFSheet sheet = null;
            // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
            sheet = wb.createSheet();
            //sheet.setColumnWidth(0, 25000);
            HSSFCellStyle cellStyle = getCellStyle(wb);
            // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
            HSSFRow row = sheet.createRow(0);
            HSSFRow row2 = sheet.createRow(1);
            int curColumn = 0;
            int lastRow = 0;
            // 第四步，创建单元格，并设置值表头 设置表头居中
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "序号");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "班级");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "姓名");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "性别");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, lastRow, curColumn, curColumn, "年龄");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, 0, curColumn, curColumn, "复查结果");
            curColumn++;
            createCell(sheet, row, centerStyle, 0, 0, curColumn, curColumn, "确诊日期");
            for (int i = 0; i < arrResult.length(); i++) {
                curColumn = 0;
                JSONObject objdata = arrResult.optJSONObject(i);
                HSSFRow contentRow = sheet.createRow(i + (lastRow + 1));
                // 第五步，创建单元格并填充数据
                HSSFCell contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(i + 1);
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("claname"));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("stuname"));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("sex"));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(getZhAgeByAge(objdata.optString("age")));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("abnormal_text"));
                curColumn++;
                contentCell = contentRow.createCell(curColumn);
                contentCell.setCellValue(objdata.optString("abnormal_time"));
            }
            DateFormat format1 = new SimpleDateFormat("yyyyMMddHHmmss");
            Random r = new Random();
            int urlrandom = r.nextInt(1000);
            Date date = new Date();
            String eurl = format1.format(date) + urlrandom + ".xls";
            filepath = Global.ProjectPath + "upload/" + eurl;
            fout = new FileOutputStream(filepath);
            wb.write(fout);
            close(fout);
            wb.close();
            objre.put("name", eurl);
            objre.put("path", "upload/" + eurl);
        } catch (Exception e) {
            objre.put("error", "生成excel出错");
            log.error("生成excel出错", e);
        } finally {
            FileUtil.close(fout, wb);
        }
        return objre.toString();
    }

    /**
     * 专案提醒幼儿复查
     * @param params
     * @param request
     * @return
     */
    public static String ReviewRemind(JSONObject params, HttpServletRequest request) {
        String Result = "";
//        ArrayList<String> InsertSqls = new ArrayList<String>();
        try {
            JSONObject rest = null;
            String detailtype = params.optString("detailtype")//消息详情源分类 1.通知类 2.预约挂号 3.预约入园体检 4预约定期体检 5付款'
                    , receivetype = params.optString("receivetype")
                    , areacode = params.optString("areacode")
                    , msgtype = params.optString("msgtype")
                    , tabletype = params.optString("tabletype")
                    , noticetitle = params.optString("noticetitle");
            JSONObject objpushmsg = params.optJSONObject("objpushmsg");
            JSONArray arrid = params.getJSONArray("arrid");
            String cguid = String.valueOf(SecurityUtils.getAttribute("guid"));//妇幼guid
            String yeyid = String.valueOf(SecurityUtils.getAttribute("yeyid"));//妇幼guid
            String sql = "";
            JSONObject objZhuanAnByMobile = new JSONObject();
            JSONArray arrmobile = new JSONArray();
            if(StringUtil.notNull(tabletype)){
                if("thinchild".equals(tabletype)){
                    detailtype = "ek_thinchild";
                    sql = "select z.yeyid,s.phone,s.mophone,s.faphone,s.stuname,to_char(m.managetime,'yyyy-mm-dd') as managetime,m.lowweight,m.thin,m.growthslow,m.yyblpj as pj,z.id,m.id as detailid,s.stuno,s.birthday,s.sex,c.classno,c.claname,yey.fuyouorganid,yey.fuyouorganname from ek_student as s,ek_thinchild as z, (select m.id,m.stunum,yeyid,m.managetime,m.lowweight,m.thin,m.growthslow,m.yyblpj from ek_thinchildmanage m INNER JOIN (SELECT stunum, MAX(managetime) AS managetime FROM ek_thinchildmanage where isdel=0  GROUP BY stunum) latest ON m.stunum = latest.stunum AND m.managetime = latest.managetime where m.isdel=0 order by m.stunum) as m, ek_class as c,yey " +
                            "where s.isdel=0 and z.isdel=0 and z.idend=0 and m.yyblpj is not null and s.yeyid=z.yeyid and s.stuno=z.stuno and s.yeyid=m.yeyid and s.stuno=m.stunum" + (arrid.length() > 0 ? " and z.id in (" + arrid.join(",") + ")": "") + " and c.isdel=0 and s.classno=c.classno and s.yeyid=c.yeyid and to_char(c.gouptime, 'yyyy-mm-dd')=(select to_char(Max(gouptime),'YYYY-MM-DD') from ek_class where isdel=0 and gouptime<=m.managetime) and yey.id=s.yeyid";
                } else if("fatchild".equals(tabletype)){
                    detailtype = "ek_fatchild";
                    sql = "select z.yeyid,s.phone,s.mophone,s.faphone,s.stuname,to_char(m.managetime,'yyyy-mm-dd') as managetime,m.wfatdivision as pj,z.id,m.id as detailid,s.stuno,s.birthday,s.sex,c.classno,c.claname,yey.fuyouorganid,yey.fuyouorganname from ek_student as s,ek_fatchild as z, (select m.id,m.stunum,yeyid,m.managetime,m.wfatdivision from ek_fatchildmanage m " +
                            "INNER JOIN (SELECT stunum, MAX(managetime) AS managetime FROM ek_fatchildmanage where isdel=0  GROUP BY stunum) latest ON m.stunum = latest.stunum AND m.managetime = latest.managetime where m.isdel=0 order by m.stunum) as m, ek_class as c,yey " +
                            "where s.isdel=0 and z.isdel=0 and z.idend=0 and s.yeyid=z.yeyid and s.stuno=z.stuno and s.yeyid=m.yeyid and s.stuno=m.stunum and m.wfatdivision is not null and m.wfatdivision!='正常' and m.wfatdivision!=''" + (arrid.length() > 0 ? " and z.id in (" + arrid.join(",") + ")" : "") + " and c.isdel=0 and s.classno=c.classno and s.yeyid=c.yeyid and to_char(c.gouptime, 'yyyy-mm-dd')=(select to_char(Max(gouptime),'YYYY-MM-DD') from ek_class where isdel=0 and gouptime<=m.managetime) and yey.id=s.yeyid";
                } else if("anemiachild".equals(tabletype)){
                    detailtype = "tb_anemiachild";
                    sql = "select z.yeyid,s.phone,s.mophone,s.faphone,s.stuname,to_char(m.managetime,'yyyy-mm-dd') as managetime,m.hematinvalue || '贫血' as pj,z.id,m.id as detailid,s.stuno,s.birthday,s.sex,c.classno,c.claname,yey.fuyouorganid,yey.fuyouorganname from ek_student as s,tb_anemiachild as z, (select m.id,m.stunum,yeyid,m.managetime,m.hematinvalue from tb_anemiamanage m " +
                            "INNER JOIN (SELECT stunum, MAX(managetime) AS managetime FROM tb_anemiamanage where isdel=0  GROUP BY stunum) latest ON m.stunum = latest.stunum AND m.managetime = latest.managetime where m.isdel=0 order by m.stunum) as m, ek_class as c,yey " +
                            "where s.isdel=0 and z.isdel=0 and z.idend=0 and s.yeyid=z.yeyid and s.stuno=z.stuno and s.yeyid=m.yeyid and s.stuno=m.stunum and m.hematinvalue is not null and m.hematinvalue!='正常' and m.hematinvalue!=''" + (arrid.length() > 0 ? " and z.id in (" + arrid.join(",") + ")" : "") + " and c.isdel=0 and s.classno=c.classno and s.yeyid=c.yeyid and to_char(c.gouptime, 'yyyy-mm-dd')=(select to_char(Max(gouptime),'YYYY-MM-DD') from ek_class where isdel=0 and gouptime<=m.managetime) and yey.id=s.yeyid";
                } else if("psychtest".equals(tabletype)){
                    detailtype = "tb_psychtest";
                    sql = "select z.yeyid,s.phone,s.mophone,s.faphone,s.stuname,to_char(m.managetime,'yyyy-mm-dd') as managetime,'心里行为发育评估异常' as pj,z.id,m.id as detailid,s.stuno,s.birthday,s.sex,c.classno,c.claname,yey.fuyouorganid,yey.fuyouorganname from ek_student as s,tb_psychtest as z, (select m.id,m.stuno,yeyid,m.managetime from tb_psychtestmanage m " +
                            "INNER JOIN (SELECT stuno, MAX(managetime) AS managetime FROM tb_psychtestmanage where isdel=0  GROUP BY stuno) latest ON m.stuno = latest.stuno AND m.managetime = latest.managetime where m.isdel=0 order by m.stuno) as m, ek_class as c,yey " +
                            "where s.isdel=0 and z.isdel=0 and z.idend=0 and s.yeyid=z.yeyid and s.stuno=z.stuno and s.yeyid=m.yeyid and s.stuno=m.stuno" + (arrid.length() > 0 ? " and z.id in (" + arrid.join(",") + ")" : "") + " and c.isdel=0 and s.classno=c.classno and s.yeyid=c.yeyid and to_char(c.gouptime, 'yyyy-mm-dd')=(select to_char(Max(gouptime),'YYYY-MM-DD') from ek_class where isdel=0 and gouptime<=m.managetime) and yey.id=s.yeyid";
                }
                JSONObject objmobile = DBUtil.selectJSON("f", cguid, sql, null, request);
                if(objmobile.getInt("length") > 0){
                    JSONArray arrm = objmobile.optJSONArray("arrdata");
                    for (int i = 0; i < arrm.length(); i++) {
                        long phone = arrm.optJSONObject(i).optLong("phone");
                        long mophone = arrm.optJSONObject(i).optLong("mophone");
                        long faphone = arrm.optJSONObject(i).optLong("faphone");
                        if(phone > 0){
                            arrmobile.put(phone);
                            String strphone = phone + "";
                            if(!objZhuanAnByMobile.has(strphone)){
                                JSONArray arrza = new JSONArray();
                                arrza.put(arrm.optJSONObject(i));
                                objZhuanAnByMobile.put(strphone, arrza);
                            } else {
                                objZhuanAnByMobile.optJSONArray(strphone).put(arrm.optJSONObject(i));
                            }
                        }
                        if(mophone > 0 && phone != mophone){
                            arrmobile.put(mophone);
                            String strphone = mophone + "";
                            if(!objZhuanAnByMobile.has(strphone)){
                                JSONArray arrza = new JSONArray();
                                arrza.put(arrm.optJSONObject(i));
                                objZhuanAnByMobile.put(strphone, arrza);
                            } else {
                                objZhuanAnByMobile.optJSONArray(strphone).put(arrm.optJSONObject(i));
                            }
                        }
                        if(faphone > 0 && phone != faphone){
                            arrmobile.put(faphone);
                            String strphone = faphone + "";
                            if(!objZhuanAnByMobile.has(strphone)){
                                JSONArray arrza = new JSONArray();
                                arrza.put(arrm.optJSONObject(i));
                                objZhuanAnByMobile.put(strphone, arrza);
                            } else {
                                objZhuanAnByMobile.optJSONArray(strphone).put(arrm.optJSONObject(i));
                            }
                        }
                    }
                }
                if(arrmobile.length() > 0){
                    JSONArray arrza = null;
                    JSONArray arropenid = new JSONArray();
                    com.alibaba.fastjson2.JSONObject objSendMsgContent = new com.alibaba.fastjson2.JSONObject();
                    com.alibaba.fastjson2.JSONObject objXcxMessageContent = null;
//                    JSONObject objopen = GetOpenInfoAreacode(areacode, request);
                    JSONObject objnoticeuser = DBUtil.selectJSON("fyyypt", "", "select w.id,w.openid_xcx,w.mobile,w.truename,w.nickname from xcxuser as w where mobile is not null and mobile!='' and w.isdel=0 and mobile in ('" + arrmobile.join("','") + "')", null, request);
                    if (objnoticeuser.getInt("length") > 0) {
                        JSONArray arruser = objnoticeuser.optJSONArray("arrdata");
                        ISaveMessageService iSaveMessageService = SpringContextUtil.getBean(ISaveMessageService.class);
                        JSONObject objza = null;
                        for (int j = 0; j < arruser.length(); j++) {
                            arrza = objZhuanAnByMobile.optJSONArray(arruser.optJSONObject(j).optString("mobile"));
                            for (int i = 0; i < arrza.length(); i++) {
                                objza = arrza.optJSONObject(i);
                                String strmsgtxt = objza.optString("stuname") + "，" + objza.optString("managetime") + "检查结果为" + objza.optString("pj") + "，请及时带宝宝去复查";
                                //保存提醒记录
                                JSONObject objzhuananremind = DBUtil.insertSql("f", cguid, "insert into zhuanan_remind_record(type,zid,detailid,yeyid, classno, claname, stuno, stuname, sex, birthday, msgtxt, title,mobile, organid, organname) values ('" + detailtype + "'," + objza.optString("id") + "," + objza.optString("detailid") + "," + objza.optString("yeyid") + ",'" + objza.optString("classno") + "','" + objza.optString("claname") + "'," + objza.optString("stuno") + ",'" + objza.optString("stuname") + "','" + objza.optString("sex") + "','" + objza.optString("birthday") + "','" + strmsgtxt + "','" + noticetitle + "','" + arruser.optJSONObject(j).optString("mobile") + "'," + objza.optString("fuyouorganid") + ",'" + objza.optString("fuyouorganname") + "')");
                                objXcxMessageContent = new com.alibaba.fastjson2.JSONObject();
                                objXcxMessageContent.put("msgtype", StringUtil.isNull(msgtype) ? "8" : msgtype);
                                objXcxMessageContent.put("titile", noticetitle);
//                                objXcxMessageContent.put("msgtime", msgtype);
                                objXcxMessageContent.put("detailid", objzhuananremind.optInt("re"));
                                objXcxMessageContent.put("yeyid", objza.optString("yeyid"));
                                objXcxMessageContent.put("fyguid", cguid);
                                objXcxMessageContent.put("receiveuid", arruser.optJSONObject(j).optString("id"));
                                objXcxMessageContent.put("receivetype", receivetype);
                                objXcxMessageContent.put("detailtype", detailtype);
                                objXcxMessageContent.put("msgtxt", strmsgtxt);
                                objXcxMessageContent.put("otherdata", "");
                                iSaveMessageService.saveMessage(objXcxMessageContent, request);
                                arropenid.put(arruser.optJSONObject(j).optString("openid_xcx"));
//                                InsertSqls.add("INSERT INTO xcx_message(msgtype, titile, msgtime, fyid, fyguid, receiveuid, receivetype,detailtype, msgtxt) VALUES (" + (StringUtil.isNull(msgtype) ? "8" : msgtype) + ",'" + noticetitle + "',now()," + objopen.optString("id") + ",'" + cguid + "'," + arruser.optJSONObject(j).optString("id") + "," + receivetype + "," + detailtype + ",'" + objza.optString("stuname") + "，" + objza.optString("managetime") + "检查结果未" + objza.optString("pj") + "，请及时带宝宝去复查');");
                            }
                        }
//                        objSendMsgContent.put("type", objpushmsg.optString("type"));
//                        objSendMsgContent.put("data", objpushmsg.optString("data"));
//                        objSendMsgContent.put("mpstate", objpushmsg.optString("mpstate"));
//                        objSendMsgContent.put("touser", arropenid);
//                        SendMessageService sendMessageService = SpringContextUtil.getBean(SendMessageService.class);
//                        sendMessageService.sendMsg(objSendMsgContent, SecurityConstants.INNER);
                        Result = "{\"re\":\"success\"}";
                    } else {
                        Result = "{\"error\":\"未查询到关注的幼儿\"}";
                    }
                } else {
                    Result = "{\"error\":\"未获取到家长信息，无法发送提醒\"}";
                }
            } else {
                Result = "{\"error\":\"没有需要提醒的幼儿\"}";
            }
        } catch (Exception e) {
            log.error(e);
            Result = "{\"error\":\"操作失败\"}";
        }
        return Result;
    }

    /***
     * 获取cguid
     * @param areacode
     * @param request
     * @return
     */
    public static JSONObject GetOpenInfoAreacode(String areacode, HttpServletRequest request) {
        JSONObject objopen = null;
        try {
            log.info("getcguid");
            areacode = areacode.substring(0,6);
            String acode = areacode; //查询开通情况，获取上报地址
            if (areacode.substring(0, 2).equals("11") || areacode.substring(0, 2).equals("12") || areacode.substring(0,
                    2).equals("50") || areacode.substring(0, 2).equals("31")) { //地区编码
                acode = areacode.substring(0, 6);
            } else {
//                acode = areacode.substring(0, 4) + "00";
                acode = areacode.substring(0, 6);
            }
            //查询下幼儿园
            JSONObject obj = DBUtil.selectJSON("fyyypt", "","SELECT cguid,fuyouname,id FROM w_open where isdel=0 and areacode='" + acode + "'" , null, request);
            if (obj.getInt("length") > 0) {
                JSONArray arrdata = obj.getJSONArray("arrdata");
                objopen = arrdata.optJSONObject(0);
//                cguid = objcguid.optString("cguid");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            e.printStackTrace();
        }
        return objopen;
    }
}
