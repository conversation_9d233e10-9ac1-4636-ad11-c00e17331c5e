package com.btkj.fuyou.wrules.check;

import com.whoami.util.StringUtil;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 标签算法执行器
 * 负责执行各种类型的标签匹配算法
 * 采用与现有项目一致的静态方法模式
 */
public class TagAlgorithmExecutor {
    private static final Logger log = org.apache.logging.log4j.LogManager.getLogger(TagAlgorithmExecutor.class);
    
    // 算法执行超时时间（毫秒）
    private static final long ALGORITHM_TIMEOUT = 5000;
    
    // 线程池用于并发执行算法
    private static final ExecutorService executorService = Executors.newFixedThreadPool(10);
    
    /**
     * 执行单个标签匹配算法
     * @param algorithm 算法对象
     * @param checkData 体检数据
     * @return 匹配结果
     */
    public static JSONObject executeAlgorithm(JSONObject algorithm, JSONObject checkData) {
        JSONObject result = new JSONObject();
        result.put("id", algorithm.optString("id"));
        result.put("labelname", algorithm.optString("labelname"));
        result.put("is_matched", false);
        result.put("match_reason", "");
        result.put("confidence", 0.0);
        
        try {
            String algorithmType = algorithm.optString("algorithm_type");
            String algorithmContent = algorithm.optString("algorithm_content");
            
            if (StringUtil.isNull(algorithmContent)) {
                result.put("match_reason", "算法内容为空");
                return result;
            }
            
            boolean matched = false;
            String reason = "";
            
            switch (algorithmType) {
                case "EXPRESSION":
                    JSONObject expResult = executeExpressionAlgorithm(algorithmContent, checkData);
                    matched = expResult.optBoolean("matched", false);
                    reason = expResult.optString("reason", "");
                    break;
                case "SCRIPT":
                    JSONObject scriptResult = executeScriptAlgorithm(algorithmContent, checkData);
                    matched = scriptResult.optBoolean("matched", false);
                    reason = scriptResult.optString("reason", "");
                    break;
                case "RULE":
                    JSONObject ruleResult = executeRuleAlgorithm(algorithmContent, checkData);
                    matched = ruleResult.optBoolean("matched", false);
                    reason = ruleResult.optString("reason", "");
                    break;
                default:
                    reason = "不支持的算法类型: " + algorithmType;
            }
            
            result.put("is_matched", matched);
            result.put("match_reason", reason);
            result.put("confidence", matched ? 1.0 : 0.0);
            log.info("++++++++++算法执行完毕：{}", result);
        } catch (Exception e) {
            log.error("算法执行异常: " + algorithm.optString("id"), e);
            result.put("match_reason", "算法执行异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 批量执行标签匹配算法
     * @param algorithms 算法列表
     * @param checkData 体检数据
     * @return 匹配结果列表
     */
    public static List<JSONObject> executeAllAlgorithms(List<JSONObject> algorithms, JSONObject checkData) {
        List<JSONObject> results = new ArrayList<>();
        
        if (algorithms == null || algorithms.isEmpty()) {
            return results;
        }
        
        // 并发执行算法
        List<Future<JSONObject>> futures = new ArrayList<>();
        for (JSONObject algorithm : algorithms) {
            Future<JSONObject> future = executorService.submit(() -> 
                safeExecuteAlgorithm(algorithm, checkData)
            );
            futures.add(future);
        }
        
        // 收集结果
        for (Future<JSONObject> future : futures) {
            try {
                JSONObject result = future.get(ALGORITHM_TIMEOUT, TimeUnit.MILLISECONDS);
                if (result != null) {
                    results.add(result);
                }
            } catch (TimeoutException e) {
                log.warn("算法执行超时");
                future.cancel(true);
            } catch (Exception e) {
                log.error("获取算法执行结果异常", e);
            }
        }
        
        return results;
    }
    
    /**
     * 安全执行算法（带异常处理）
     * @param algorithm 算法对象
     * @param checkData 体检数据
     * @return 匹配结果
     */
    private static JSONObject safeExecuteAlgorithm(JSONObject algorithm, JSONObject checkData) {
        try {
            return executeAlgorithm(algorithm, checkData);
        } catch (Exception e) {
            log.error("安全执行算法异常: " + algorithm.optString("id"), e);
            JSONObject result = new JSONObject();
            result.put("id", algorithm.optString("id"));
            result.put("labelname", algorithm.optString("labelname"));
            result.put("is_matched", false);
            result.put("match_reason", "算法执行异常: " + e.getMessage());
            result.put("confidence", 0.0);
            return result;
        }
    }
    
    /**
     * 执行表达式算法
     * @param expression 表达式内容
     * @param checkData 体检数据
     * @return 执行结果
     */
    private static JSONObject executeExpressionAlgorithm(String expression, JSONObject checkData) {
        JSONObject result = new JSONObject();
        result.put("matched", false);
        result.put("reason", "");
        
        try {
            // 替换表达式中的变量
            String processedExpression = replaceVariables(expression, checkData);
            
            // 使用JavaScript引擎计算表达式
            ScriptEngine engine = new ScriptEngineManager().getEngineByName("javascript");
            Object evalResult = engine.eval(processedExpression);
            
            boolean matched = Boolean.parseBoolean(evalResult.toString());
            result.put("matched", matched);
            result.put("reason", matched ? "表达式匹配成功: " + processedExpression : "表达式匹配失败: " + processedExpression);
            
        } catch (Exception e) {
            result.put("reason", "表达式执行异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行脚本算法
     * @param script 脚本内容
     * @param checkData 体检数据
     * @return 执行结果
     */
    private static JSONObject executeScriptAlgorithm(String script, JSONObject checkData) {
        JSONObject result = new JSONObject();
        result.put("matched", false);
        result.put("reason", "");
        
        try {
            ScriptEngine engine = new ScriptEngineManager().getEngineByName("javascript");
            
            // 将体检数据注入到脚本环境中
            for (String key : checkData.keySet()) {
                engine.put(key, checkData.opt(key));
            }
            
            // 执行脚本
            Object evalResult = engine.eval(script);
            
            boolean matched = Boolean.parseBoolean(evalResult.toString());
            result.put("matched", matched);
            result.put("reason", matched ? "脚本匹配成功:"+script : "脚本匹配失败:"+script);
            
        } catch (Exception e) {
            result.put("reason", "脚本执行异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行规则算法
     * @param rule 规则内容
     * @param checkData 体检数据
     * @return 执行结果
     */
    private static JSONObject executeRuleAlgorithm(String rule, JSONObject checkData) {
        JSONObject result = new JSONObject();
        result.put("matched", false);
        result.put("reason", "");
        
        try {
            // 简单的规则解析器，支持基本的条件判断
            // 格式: field operator value [AND/OR field operator value]
            boolean matched = evaluateRule(rule, checkData);
            result.put("matched", matched);
            result.put("reason", matched ? "规则匹配成功: " + rule : "规则匹配失败: " + rule);
            
        } catch (Exception e) {
            result.put("reason", "规则执行异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 替换表达式中的变量
     * @param expression 原始表达式
     * @param checkData 体检数据
     * @return 处理后的表达式
     */
    private static String replaceVariables(String expression, JSONObject checkData) {
        String result = expression;
        
        // 替换变量，格式: ${variable_name}
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(expression);
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = checkData.opt(variableName);
            String replacement = value != null ? value.toString() : "null";
            result = result.replace("${" + variableName + "}", replacement);
        }
        
        return result;
    }
    
    /**
     * 评估规则表达式
     * @param rule 规则字符串
     * @param checkData 体检数据
     * @return 评估结果
     */
    private static boolean evaluateRule(String rule, JSONObject checkData) {
        try {
            // 1. 预处理规则，转换逻辑操作符
            String preprocessedRule = preprocessRule(rule);
            log.debug("预处理后的规则: {}", preprocessedRule);
            
            // 2. 替换变量
            String processedRule = replaceVariables(preprocessedRule, checkData);
            log.debug("变量替换后的规则: {}", processedRule);
            
            // 3. 执行 JavaScript 评估
            ScriptEngine engine = new ScriptEngineManager().getEngineByName("javascript");
            Object result = engine.eval(processedRule);
            
            // 4. 安全的布尔值转换
            if (result == null) {
                return false;
            }
            
            if (result instanceof Boolean) {
                return (Boolean) result;
            }
            
            String resultStr = result.toString().trim().toLowerCase();
            return "true".equals(resultStr);
            
        } catch (Exception e) {
            log.error("规则评估异常: " + rule + ", 处理后: " + replaceVariables(preprocessRule(rule), checkData) + ", 错误: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 预处理规则，将常见的逻辑操作符转换为JavaScript兼容的格式
     * @param rule 原始规则
     * @return 处理后的规则
     */
    private static String preprocessRule(String rule) {
        if (rule == null || rule.isEmpty()) {
            return rule;
        }
        
        String processed = rule;
        
        // 使用更精确的正则表达式，支持大小写不敏感
        // 转换 AND -> &&（包括前后可能的空格）
        processed = processed.replaceAll("(?i)\\s+AND\\s+", " && ");
        
        // 转换 OR -> ||（包括前后可能的空格）
        processed = processed.replaceAll("(?i)\\s+OR\\s+", " || ");
        
        // 转换 NOT -> !（需要特别处理，避免影响其他单词）
        processed = processed.replaceAll("(?i)\\bNOT\\s+", "!");
        
        // 清理多余的空格
        processed = processed.replaceAll("\\s+", " ").trim();
        
        return processed;
    }
}